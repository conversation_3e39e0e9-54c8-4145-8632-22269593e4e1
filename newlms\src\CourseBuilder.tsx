import React, { useState } from "react";

const CourseBuilder: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [courseTitle, setCourseTitle] = useState("");
  const [courseDescription, setCourseDescription] = useState("");
  const [coursePrice, setCoursePrice] = useState(0);

  const steps = [
    { id: 1, title: "Basic Information", icon: "📝" },
    { id: 2, title: "Course Content", icon: "📚" },
    { id: 3, title: "Learning Objectives", icon: "🎯" },
    { id: 4, title: "Pricing & Publishing", icon: "💰" },
    { id: 5, title: "Preview & Publish", icon: "🚀" },
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Course Builder</h1>
            <p className="text-gray-600 text-sm">Create and manage your online course</p>
          </div>
          <div className="flex items-center gap-3">
            <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
              Save Draft
            </button>
            <button className="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition">
              Preview
            </button>
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
              Publish Course
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white border-r min-h-screen p-6">
          <div className="space-y-2">
            {steps.map((step) => (
              <button
                key={step.id}
                onClick={() => setCurrentStep(step.id)}
                className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition ${
                  currentStep === step.id
                    ? "bg-purple-100 text-purple-700 border border-purple-200"
                    : "text-gray-600 hover:bg-gray-50"
                }`}
              >
                <span className="text-lg">{step.icon}</span>
                <div>
                  <div className="font-medium">{step.title}</div>
                  <div className="text-xs text-gray-500">Step {step.id}</div>
                </div>
                {currentStep === step.id && (
                  <div className="ml-auto w-2 h-2 bg-purple-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="max-w-4xl">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Basic Course Information</h2>
                <p className="text-gray-600">Provide the essential details about your course</p>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Title *
                    </label>
                    <input
                      type="text"
                      value={courseTitle}
                      onChange={(e) => setCourseTitle(e.target.value)}
                      placeholder="Enter your course title"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Description *
                    </label>
                    <textarea
                      value={courseDescription}
                      onChange={(e) => setCourseDescription(e.target.value)}
                      placeholder="Describe what students will learn in this course"
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Price (USD)
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-2 text-gray-500">$</span>
                      <input
                        type="number"
                        value={coursePrice}
                        onChange={(e) => setCoursePrice(parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Course Content */}
          {currentStep === 2 && (
            <div className="max-w-6xl">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Course Content</h2>
                <p className="text-gray-600">Organize your course into sections and lessons</p>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center py-12">
                  <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Add Your First Section</h3>
                  <p className="text-gray-600 mb-4">Start building your course by adding sections and lessons</p>
                  <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
                    Add Section
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Learning Objectives */}
          {currentStep === 3 && (
            <div className="max-w-4xl">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Learning Objectives</h2>
                <p className="text-gray-600">Define what students will learn and achieve</p>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center py-12">
                  <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Define Learning Objectives</h3>
                  <p className="text-gray-600 mb-4">What will students be able to do after completing your course?</p>
                  <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
                    Add Objective
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Pricing & Publishing */}
          {currentStep === 4 && (
            <div className="max-w-4xl">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Pricing & Publishing</h2>
                <p className="text-gray-600">Set your course price and publishing options</p>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center py-12">
                  <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Course Pricing</h3>
                  <p className="text-gray-600 mb-4">Current price: ${coursePrice.toFixed(2)}</p>
                  <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
                    Update Pricing
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Preview & Publish */}
          {currentStep === 5 && (
            <div className="max-w-4xl">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Preview & Publish</h2>
                <p className="text-gray-600">Review your course before publishing</p>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center py-12">
                  <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Publish</h3>
                  <p className="text-gray-600 mb-4">Course: {courseTitle || "Untitled Course"}</p>
                  <div className="flex items-center justify-center gap-4">
                    <button className="px-6 py-3 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition">
                      Preview Course
                    </button>
                    <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
                      Publish Course
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className={`px-6 py-2 rounded-lg transition ${
                currentStep === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Previous
            </button>
            <div className="flex items-center gap-3">
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                Save Draft
              </button>
              <button
                onClick={nextStep}
                disabled={currentStep === steps.length}
                className={`px-6 py-2 rounded-lg transition ${
                  currentStep === steps.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-purple-600 text-white hover:bg-purple-700"
                }`}
              >
                {currentStep === steps.length ? "Completed" : "Next"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseBuilder;
