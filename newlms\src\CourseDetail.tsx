import React, { useState } from "react";
// import { <PERSON><PERSON> } from "@/components/ui/button"; // Uncomment if shadcn/ui Button exists

// Type definitions
interface Instructor {
  name: string;
  avatar: string;
  stats: string;
  recommend: string;
}

interface CourseItem {
  type: "doc" | "video" | "quiz" | "module";
  label: string;
  duration: string;
}

interface CourseWeek {
  title: string;
  progress: number;
  items: CourseItem[];
}

interface CourseContent {
  sections: number;
  lectures: number;
  hours: number;
  weeks: CourseWeek[];
}

interface Course {
  title: string;
  rating: number;
  reviews: number;
  description: string;
  instructor: Instructor;
  price: number;
  oldPrice: number;
  sale: number;
  image: string;
  includes: string[];
  trial: boolean;
  content: CourseContent;
}

const mockCourseEn: Course = {
  title: "Beginner's Guide to Successful Company Management: Business and User Goals",
  rating: 4.9,
  reviews: 236,
  description:
    "Hello Student! 👋 Are you ready to embark on a comprehensive journey into the realm of successful company management with our Beginner's Guide course? During this course I'll help you delve deep into the intricacies of business strategy, organizational dynamics, and user-centric approaches. Ready to join? Send me a message and let's start! 🚀",
  instructor: {
    name: "<PERSON>",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    stats: "250+ students bought this course",
    recommend: "98% students recommend this course",
  },
  price: 87.99,
  oldPrice: 183.0,
  sale: 35,
  image: "https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80",
  includes: [
    "65 hours on demand video",
    "45 downloadable resources",
    "Access on mobile and TV",
    "86 articles",
    "30 min personal weekly session",
    "Meeting with Oxford Professor",
    "Certificate of completion",
  ],
  trial: true,
  content: {
    sections: 24,
    lectures: 490,
    hours: 72,
    weeks: [
      {
        title: "Week 1 - Beginner - Introduction to Business Management",
        progress: 22,
        items: [
          { type: "doc", label: "Read before you start", duration: "4min" },
          { type: "video", label: "Introduction to Business Foundations & Principals of Management", duration: "1h 10min" },
          { type: "video", label: "Introduction to Brand Management: Aligning Business, Brand and Behaviour", duration: "1h 23min" },
          { type: "video", label: "Business Analysis & Process Management", duration: "43min" },
          { type: "doc", label: "Major terms from the section", duration: "5 min" },
          { type: "quiz", label: "Practice analyse", duration: "1 Question" },
          { type: "module", label: "Module 1", duration: "24 Questions" },
        ],
      },
    ],
  },
};

const mockCourseAr: Course = {
  title: "دليل المبتدئين لإدارة الشركات الناجحة: أهداف العمل والمستخدم",
  rating: 4.9,
  reviews: 236,
  description:
    "مرحبًا أيها الطالب! 👋 هل أنت مستعد لبدء رحلة شاملة في عالم إدارة الشركات الناجحة مع دليل المبتدئين الخاص بنا؟ خلال هذه الدورة سأساعدك على التعمق في استراتيجيات الأعمال وديناميكيات التنظيم ونهج التركيز على المستخدم. مستعد للانضمام؟ أرسل لي رسالة ولنبدأ! 🚀",
  instructor: {
    name: "جيني ويلسون",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    stats: "أكثر من 250 طالب اشتروا هذه الدورة",
    recommend: "98% من الطلاب يوصون بهذه الدورة",
  },
  price: 87.99,
  oldPrice: 183.0,
  sale: 35,
  image: "https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80",
  includes: [
    "65 ساعة فيديو عند الطلب",
    "45 موردًا قابلًا للتنزيل",
    "الوصول عبر الجوال والتلفاز",
    "86 مقالة",
    "جلسة أسبوعية شخصية لمدة 30 دقيقة",
    "اجتماع مع أستاذ من أكسفورد",
    "شهادة إتمام",
  ],
  trial: true,
  content: {
    sections: 24,
    lectures: 490,
    hours: 72,
    weeks: [
      {
        title: "الأسبوع 1 - المبتدئ - مقدمة في إدارة الأعمال",
        progress: 22,
        items: [
          { type: "doc", label: "اقرأ قبل أن تبدأ", duration: "4 دقائق" },
          { type: "video", label: "مقدمة في أسس الأعمال ومبادئ الإدارة", duration: "1س 10د" },
          { type: "video", label: "مقدمة في إدارة العلامة التجارية: مواءمة الأعمال والعلامة والسلوك", duration: "1س 23د" },
          { type: "video", label: "تحليل الأعمال وإدارة العمليات", duration: "43 دقيقة" },
          { type: "doc", label: "المصطلحات الرئيسية من القسم", duration: "5 دقائق" },
          { type: "quiz", label: "تحليل عملي", duration: "سؤال واحد" },
          { type: "module", label: "الوحدة 1", duration: "24 سؤالاً" },
        ],
      },
    ],
  },
};

const iconMap: Record<string, any> = {
  video: (
    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z" /></svg>
  ),
  doc: (
    <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M7 7h10M7 11h10M7 15h6" /></svg>
  ),
  quiz: (
    <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
  ),
  module: (
    <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path strokeLinecap="round" strokeLinejoin="round" d="M8 12h8" /></svg>
  ),
};

function CourseDetail() {
  const [lang, setLang] = useState("en");
  const course = lang === "ar" ? mockCourseAr : mockCourseEn;
  const dir = lang === "ar" ? "rtl" : "ltr";
  const isAr = lang === "ar";

  // Text translations
  const t = {
    en: {
      courses: "Courses",
      popular: "Popular courses",
      basedOn: "based on",
      reviews: "reviews",
      courseContent: "Course content",
      sections: "sections",
      lectures: "lectures",
      hours: "hours total length",
      expand: "Expand all sections",
      buy: "Buy course now",
      message: "Send message to teacher",
      includes: "This course includes",
      trial: "10 min trial course",
      trialDesc: "Have a look and feel at the course with a quick trial.",
      preview: "Preview",
    },
    ar: {
      courses: "الدورات",
      popular: "الدورات الشائعة",
      basedOn: "بناءً على",
      reviews: "مراجعة",
      courseContent: "محتوى الدورة",
      sections: "أقسام",
      lectures: "محاضرات",
      hours: "إجمالي الساعات",
      expand: "توسيع جميع الأقسام",
      buy: "اشترِ الدورة الآن",
      message: "أرسل رسالة للمدرس",
      includes: "تشمل هذه الدورة",
      trial: "دورة تجريبية لمدة 10 دقائق",
      trialDesc: "اطلع على الدورة بسرعة.",
      preview: "معاينة",
    },
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8" dir={dir}>
      {/* Header with Language Toggle */}
      <div className={`flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4 ${isAr ? 'text-right' : ''}`}>
        <div className="text-sm text-gray-500 flex items-center gap-2">
          <span>{t[lang].courses}</span>
          <span>/</span>
          <span>{t[lang].popular}</span>
          <span>/</span>
          <span className="font-medium text-gray-700">{course.title}</span>
        </div>
        <div className="flex items-center gap-2">
          <input className="border rounded px-3 py-1 text-sm w-64" placeholder={lang === 'ar' ? 'بحث' : 'Search'} />
          <img src={course.instructor.avatar} alt="User" className="w-8 h-8 rounded-full border" />
          <button
            className="ml-2 px-3 py-1 rounded bg-violet-600 text-white text-xs font-semibold hover:bg-violet-700 transition"
            onClick={() => setLang(lang === "en" ? "ar" : "en")}
          >
            {lang === "en" ? "العربية" : "English"}
          </button>
        </div>
      </div>
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left/Main */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow p-6 mb-6">
            <div className={`flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4 ${isAr ? 'text-right' : ''}`}>
              <div>
                <div className="flex items-center gap-2 text-yellow-500 text-lg font-semibold">
                  <span>★ {course.rating}</span>
                  <span className="text-gray-500 text-sm font-normal">{t[lang].basedOn} {course.reviews} {t[lang].reviews}</span>
                </div>
                <h1 className="text-2xl md:text-3xl font-bold mt-2 mb-2 text-gray-900">
                  {course.title}
                </h1>
                <p className="text-gray-600 max-w-2xl text-sm mb-2">{course.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  <img src={course.instructor.avatar} alt="Instructor" className="w-8 h-8 rounded-full border" />
                  <span className="font-medium text-gray-700 text-sm">{course.instructor.name}</span>
                  <span className="text-gray-400 text-xs">•</span>
                  <span className="text-gray-500 text-xs">{course.instructor.stats}</span>
                  <span className="text-gray-400 text-xs">•</span>
                  <span className="text-gray-500 text-xs">{course.instructor.recommend}</span>
                </div>
              </div>
              <button className="ml-auto bg-gray-100 hover:bg-gray-200 rounded-full p-2 text-gray-500">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" /></svg>
              </button>
            </div>
          </div>
          {/* Course Content */}
          <div className="bg-white rounded-xl shadow p-6">
            <div className="flex items-center gap-4 mb-4">
              <span className="text-gray-700 font-semibold">{t[lang].courseContent}</span>
              <span className="text-xs text-gray-500">{course.content.sections} {t[lang].sections}</span>
              <span className="text-xs text-gray-500">{course.content.lectures} {t[lang].lectures}</span>
              <span className="text-xs text-gray-500">{course.content.hours} {t[lang].hours}</span>
              <button className="ml-auto text-violet-600 text-xs font-medium hover:underline">{t[lang].expand}</button>
            </div>
            {/* Week 1 Example */}
            {course.content.weeks.map((week, i) => (
              <div key={i} className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-semibold text-gray-800">{week.title}</span>
                  <span className="ml-2 text-xs text-violet-600 bg-violet-50 rounded-full px-2 py-0.5">{week.progress}%</span>
                </div>
                <ul className="divide-y divide-gray-100">
                  {week.items.map((item, j) => (
                    <li key={j} className="flex items-center gap-3 py-2">
                      {iconMap[item.type]}
                      <span className="text-gray-700 text-sm flex-1">{item.label}</span>
                      <span className="text-xs text-gray-400">{item.duration}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
        {/* Sidebar */}
        <aside className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow p-6 mb-6 flex flex-col items-center">
            <img src={course.image} alt="Course" className="w-40 h-32 object-cover rounded-lg mb-4" />
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold text-gray-900">${course.price.toFixed(2)}</span>
              <span className="text-sm line-through text-gray-400">${course.oldPrice.toFixed(2)}</span>
              <span className="text-xs bg-orange-100 text-orange-600 rounded px-2 py-0.5">{course.sale}% sale</span>
            </div>
            <button className="w-full bg-violet-600 hover:bg-violet-700 text-white font-semibold py-2 rounded-lg mb-2 transition">{t[lang].buy}</button>
            <button className="w-full border border-gray-200 hover:bg-gray-50 text-gray-700 font-medium py-2 rounded-lg mb-4 transition">{t[lang].message}</button>
            <div className="w-full">
              <span className="font-semibold text-gray-700 text-sm mb-2 block">{t[lang].includes}</span>
              <ul className="text-xs text-gray-600 space-y-1 mb-4">
                {course.includes.map((inc, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                    {inc}
                  </li>
                ))}
              </ul>
            </div>
            {course.trial && (
              <div className="w-full bg-gray-100 rounded-lg p-3 mt-2 text-center">
                <span className="font-semibold text-gray-700 text-sm">{t[lang].trial}</span>
                <p className="text-xs text-gray-500 mt-1">{t[lang].trialDesc}</p>
                <button className="mt-2 w-full bg-black text-white rounded py-1 text-xs font-medium">{t[lang].preview}</button>
              </div>
            )}
          </div>
        </aside>
      </div>
    </div>
  );
}

export default CourseDetail; 