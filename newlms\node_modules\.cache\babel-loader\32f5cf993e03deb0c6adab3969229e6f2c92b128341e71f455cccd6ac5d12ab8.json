{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\CourseDetail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\n\n// Type definitions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst mockCourseEn = {\n  title: \"Beginner's Guide to Successful Company Management: Business and User Goals\",\n  rating: 4.9,\n  reviews: 236,\n  description: \"Hello Student! 👋 Are you ready to embark on a comprehensive journey into the realm of successful company management with our Beginner's Guide course? During this course I'll help you delve deep into the intricacies of business strategy, organizational dynamics, and user-centric approaches. Ready to join? Send me a message and let's start! 🚀\",\n  instructor: {\n    name: \"<PERSON>\",\n    avatar: \"https://randomuser.me/api/portraits/women/44.jpg\",\n    stats: \"250+ students bought this course\",\n    recommend: \"98% students recommend this course\"\n  },\n  price: 87.99,\n  oldPrice: 183.0,\n  sale: 35,\n  image: \"https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80\",\n  includes: [\"65 hours on demand video\", \"45 downloadable resources\", \"Access on mobile and TV\", \"86 articles\", \"30 min personal weekly session\", \"Meeting with Oxford Professor\", \"Certificate of completion\"],\n  trial: true,\n  content: {\n    sections: 24,\n    lectures: 490,\n    hours: 72,\n    weeks: [{\n      title: \"Week 1 - Beginner - Introduction to Business Management\",\n      progress: 22,\n      items: [{\n        type: \"doc\",\n        label: \"Read before you start\",\n        duration: \"4min\"\n      }, {\n        type: \"video\",\n        label: \"Introduction to Business Foundations & Principals of Management\",\n        duration: \"1h 10min\"\n      }, {\n        type: \"video\",\n        label: \"Introduction to Brand Management: Aligning Business, Brand and Behaviour\",\n        duration: \"1h 23min\"\n      }, {\n        type: \"video\",\n        label: \"Business Analysis & Process Management\",\n        duration: \"43min\"\n      }, {\n        type: \"doc\",\n        label: \"Major terms from the section\",\n        duration: \"5 min\"\n      }, {\n        type: \"quiz\",\n        label: \"Practice analyse\",\n        duration: \"1 Question\"\n      }, {\n        type: \"module\",\n        label: \"Module 1\",\n        duration: \"24 Questions\"\n      }]\n    }]\n  }\n};\nconst mockCourseAr = {\n  title: \"دليل المبتدئين لإدارة الشركات الناجحة: أهداف العمل والمستخدم\",\n  rating: 4.9,\n  reviews: 236,\n  description: \"مرحبًا أيها الطالب! 👋 هل أنت مستعد لبدء رحلة شاملة في عالم إدارة الشركات الناجحة مع دليل المبتدئين الخاص بنا؟ خلال هذه الدورة سأساعدك على التعمق في استراتيجيات الأعمال وديناميكيات التنظيم ونهج التركيز على المستخدم. مستعد للانضمام؟ أرسل لي رسالة ولنبدأ! 🚀\",\n  instructor: {\n    name: \"جيني ويلسون\",\n    avatar: \"https://randomuser.me/api/portraits/women/44.jpg\",\n    stats: \"أكثر من 250 طالب اشتروا هذه الدورة\",\n    recommend: \"98% من الطلاب يوصون بهذه الدورة\"\n  },\n  price: 87.99,\n  oldPrice: 183.0,\n  sale: 35,\n  image: \"https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80\",\n  includes: [\"65 ساعة فيديو عند الطلب\", \"45 موردًا قابلًا للتنزيل\", \"الوصول عبر الجوال والتلفاز\", \"86 مقالة\", \"جلسة أسبوعية شخصية لمدة 30 دقيقة\", \"اجتماع مع أستاذ من أكسفورد\", \"شهادة إتمام\"],\n  trial: true,\n  content: {\n    sections: 24,\n    lectures: 490,\n    hours: 72,\n    weeks: [{\n      title: \"الأسبوع 1 - المبتدئ - مقدمة في إدارة الأعمال\",\n      progress: 22,\n      items: [{\n        type: \"doc\",\n        label: \"اقرأ قبل أن تبدأ\",\n        duration: \"4 دقائق\"\n      }, {\n        type: \"video\",\n        label: \"مقدمة في أسس الأعمال ومبادئ الإدارة\",\n        duration: \"1س 10د\"\n      }, {\n        type: \"video\",\n        label: \"مقدمة في إدارة العلامة التجارية: مواءمة الأعمال والعلامة والسلوك\",\n        duration: \"1س 23د\"\n      }, {\n        type: \"video\",\n        label: \"تحليل الأعمال وإدارة العمليات\",\n        duration: \"43 دقيقة\"\n      }, {\n        type: \"doc\",\n        label: \"المصطلحات الرئيسية من القسم\",\n        duration: \"5 دقائق\"\n      }, {\n        type: \"quiz\",\n        label: \"تحليل عملي\",\n        duration: \"سؤال واحد\"\n      }, {\n        type: \"module\",\n        label: \"الوحدة 1\",\n        duration: \"24 سؤالاً\"\n      }]\n    }]\n  }\n};\nconst iconMap = {\n  video: /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5 text-blue-500\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"2\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 114\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this),\n  doc: /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5 text-green-500\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"2\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      d: \"M7 7h10M7 11h10M7 15h6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 115\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this),\n  quiz: /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5 text-yellow-500\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"2\",\n    viewBox: \"0 0 24 24\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 116\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this),\n  module: /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"w-5 h-5 text-purple-500\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"2\",\n    viewBox: \"0 0 24 24\",\n    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n      cx: \"12\",\n      cy: \"12\",\n      r: \"10\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 116\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      d: \"M8 12h8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 149\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this)\n};\n\n// Sidebar Navigation Component\nconst SidebarNav = () => {\n  const navItems = [{\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this),\n    active: true\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this),\n    active: false\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this),\n    active: false\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this),\n    active: false\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this),\n    active: false\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this),\n    active: false\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"2\",\n        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this),\n    active: false\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed left-0 top-0 h-full w-16 bg-gray-900 flex flex-col items-center py-4 z-10\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-10 h-10 bg-white rounded-lg flex items-center justify-center mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-6 h-6 text-gray-900\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l5 6.625 5-6.625h-2.975c.257-3.351 3.06-6 6.475-6 3.584 0 6.5 2.916 6.5 6.5s-2.916 6.5-6.5 6.5c-1.863 0-3.542-.793-4.728-2.053l-2.427 3.216c1.877 1.754 4.389 2.837 7.155 2.837 5.79 0 10.5-4.71 10.5-10.5s-4.71-10.5-10.5-10.5z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), navItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-10 h-10 rounded-lg flex items-center justify-center mb-2 cursor-pointer transition-colors ${item.active ? 'bg-purple-600 text-white' : 'text-gray-400 hover:bg-gray-800'}`,\n      children: item.icon\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 219,\n    columnNumber: 5\n  }, this);\n};\n_c = SidebarNav;\nfunction CourseDetail() {\n  _s();\n  const [lang, setLang] = useState(\"en\");\n  const [expandedWeeks, setExpandedWeeks] = useState([0]); // First week expanded by default\n  const [showPreview, setShowPreview] = useState(false);\n  const [completedItems, setCompletedItems] = useState([]);\n  const course = lang === \"ar\" ? mockCourseAr : mockCourseEn;\n  const dir = lang === \"ar\" ? \"rtl\" : \"ltr\";\n  const toggleWeek = weekIndex => {\n    setExpandedWeeks(prev => prev.includes(weekIndex) ? prev.filter(i => i !== weekIndex) : [...prev, weekIndex]);\n  };\n  const toggleItemCompletion = itemId => {\n    setCompletedItems(prev => prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]);\n  };\n\n  // Text translations\n  const t = {\n    en: {\n      courses: \"Courses\",\n      popular: \"Popular courses\",\n      basedOn: \"based on\",\n      reviews: \"reviews\",\n      courseContent: \"Course content\",\n      sections: \"sections\",\n      lectures: \"lectures\",\n      hours: \"hours total length\",\n      expand: \"Expand all sections\",\n      buy: \"Buy course now\",\n      message: \"Send message to teacher\",\n      includes: \"This course includes\",\n      trial: \"10 min trial course\",\n      trialDesc: \"Have a look and feel at the course with a quick trial.\",\n      preview: \"Preview\"\n    },\n    ar: {\n      courses: \"الدورات\",\n      popular: \"الدورات الشائعة\",\n      basedOn: \"بناءً على\",\n      reviews: \"مراجعة\",\n      courseContent: \"محتوى الدورة\",\n      sections: \"أقسام\",\n      lectures: \"محاضرات\",\n      hours: \"إجمالي الساعات\",\n      expand: \"توسيع جميع الأقسام\",\n      buy: \"اشترِ الدورة الآن\",\n      message: \"أرسل رسالة للمدرس\",\n      includes: \"تشمل هذه الدورة\",\n      trial: \"دورة تجريبية لمدة 10 دقائق\",\n      trialDesc: \"اطلع على الدورة بسرعة.\",\n      preview: \"معاينة\"\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    dir: dir,\n    children: [/*#__PURE__*/_jsxDEV(SidebarNav, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ml-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-b px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\",\n                placeholder: lang === 'ar' ? 'بحث' : 'Search'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 text-gray-400 absolute left-3 top-3\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M15 17h5l-5 5v-5zM9 7H4l5-5v5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: course.instructor.avatar,\n              alt: \"User\",\n              className: \"w-8 h-8 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-3 py-1 rounded bg-purple-600 text-white text-xs font-semibold hover:bg-purple-700 transition\",\n              onClick: () => setLang(lang === \"en\" ? \"ar\" : \"en\"),\n              children: lang === \"en\" ? \"العربية\" : \"English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: t[lang].courses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t[lang].popular\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-gray-700\",\n            children: course.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 pb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 text-yellow-500 text-sm font-medium mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2605 \", course.rating]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 text-sm font-normal\",\n                  children: [t[lang].basedOn, \" \", course.reviews, \" \", t[lang].reviews]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"ml-auto bg-gray-100 hover:bg-gray-200 rounded-full p-2 text-gray-500\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold mb-4 text-gray-900 leading-tight\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-base mb-6 leading-relaxed\",\n                children: course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: course.instructor.avatar,\n                  alt: \"Instructor\",\n                  className: \"w-10 h-10 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium text-gray-900 text-sm\",\n                    children: course.instructor.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4 text-xs text-gray-500 mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.instructor.stats\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: course.instructor.recommend\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg border border-gray-200 p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: t[lang].courseContent\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-purple-600 text-sm font-medium hover:underline\",\n                  children: t[lang].expand\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-6 mb-6 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [course.content.sections, \" \", t[lang].sections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [course.content.lectures, \" \", t[lang].lectures]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [course.content.hours, \" \", t[lang].hours]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), course.content.weeks.map((week, i) => {\n                const isExpanded = expandedWeeks.includes(i);\n                const completedCount = week.items.filter(item => completedItems.includes(`${i}-${item.label}`)).length;\n                const progressPercent = completedCount / week.items.length * 100;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg mb-4 overflow-hidden\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleWeek(i),\n                    className: \"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: `w-4 h-4 text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: \"2\",\n                          d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 430,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-900\",\n                        children: week.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 432,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-purple-600 bg-purple-100 rounded-full px-2 py-1\",\n                          children: [Math.round(progressPercent), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 434,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [completedCount, \"/\", week.items.length, \" completed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-2 bg-gray-200 rounded-full overflow-hidden\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-full bg-purple-600 transition-all duration-300\",\n                        style: {\n                          width: `${progressPercent}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4\",\n                      children: week.items.map((item, j) => {\n                        const itemId = `${i}-${item.label}`;\n                        const isCompleted = completedItems.includes(itemId);\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-3 py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded px-2 transition-colors group\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => toggleItemCompletion(itemId),\n                            className: `w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${isCompleted ? 'bg-green-500 border-green-500 text-white' : 'border-gray-300 hover:border-green-400'}`,\n                            children: isCompleted && /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"w-3 h-3\",\n                              fill: \"none\",\n                              stroke: \"currentColor\",\n                              viewBox: \"0 0 24 24\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M5 13l4 4L19 7\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 473,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 472,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 463,\n                            columnNumber: 33\n                          }, this), iconMap[item.type], /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-sm flex-1 transition-colors ${isCompleted ? 'text-gray-500 line-through' : 'text-gray-700'}`,\n                            children: item.label\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 478,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-500\",\n                            children: item.duration\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 483,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded\",\n                            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"w-4 h-4 text-gray-400\",\n                              fill: \"none\",\n                              stroke: \"currentColor\",\n                              viewBox: \"0 0 24 24\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 486,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 485,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 484,\n                            columnNumber: 33\n                          }, this)]\n                        }, j, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, i, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n            className: \"lg:col-span-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: course.image,\n                  alt: \"Course\",\n                  className: \"w-full h-48 object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 right-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-white/90 backdrop-blur-sm rounded-full p-2 text-gray-600 hover:bg-white transition\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-3xl font-bold text-gray-900\",\n                    children: [\"$\", course.price.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg line-through text-gray-400\",\n                    children: [\"$\", course.oldPrice.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs bg-orange-100 text-orange-600 rounded-full px-2 py-1 font-medium\",\n                    children: [course.sale, \"% sale\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-lg transition flex items-center justify-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 23\n                    }, this), t[lang].buy]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full border border-gray-200 hover:bg-gray-50 text-gray-700 font-medium py-3 rounded-lg transition flex items-center justify-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this), t[lang].message]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900 text-sm mb-3\",\n                    children: t[lang].includes\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: course.includes.map((inc, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-center gap-3 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-4 h-4 text-green-500 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 543,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 542,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: inc\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 27\n                      }, this)]\n                    }, i, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this), course.trial && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-900 rounded-lg p-4 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-white text-sm mb-2\",\n                    children: t[lang].trial\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-300 text-xs mb-3\",\n                    children: t[lang].trialDesc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-white text-gray-900 rounded-lg py-2 text-sm font-medium hover:bg-gray-100 transition\",\n                    children: t[lang].preview\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n}\n_s(CourseDetail, \"DiNLcDAZmTYPPE0uBOwc++rdfqs=\");\n_c2 = CourseDetail;\nexport default CourseDetail;\nvar _c, _c2;\n$RefreshReg$(_c, \"SidebarNav\");\n$RefreshReg$(_c2, \"CourseDetail\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "mockCourseEn", "title", "rating", "reviews", "description", "instructor", "name", "avatar", "stats", "recommend", "price", "oldPrice", "sale", "image", "includes", "trial", "content", "sections", "lectures", "hours", "weeks", "progress", "items", "type", "label", "duration", "mockCourseAr", "iconMap", "video", "className", "fill", "stroke", "strokeWidth", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "doc", "quiz", "module", "cx", "cy", "r", "SidebarNav", "navItems", "icon", "active", "map", "item", "index", "_c", "CourseDetail", "_s", "lang", "setLang", "expandedWeeks", "setExpandedWeeks", "showPreview", "setShowPreview", "completedItems", "setCompletedItems", "course", "dir", "toggleWeek", "weekIndex", "prev", "filter", "i", "toggleItemCompletion", "itemId", "id", "t", "en", "courses", "popular", "basedOn", "courseContent", "expand", "buy", "message", "trialDesc", "preview", "ar", "placeholder", "src", "alt", "onClick", "week", "isExpanded", "completedCount", "length", "progressPercent", "Math", "round", "style", "width", "j", "isCompleted", "toFixed", "inc", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/CourseDetail.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\n\r\n// Type definitions\r\ninterface Instructor {\r\n  name: string;\r\n  avatar: string;\r\n  stats: string;\r\n  recommend: string;\r\n}\r\n\r\ninterface CourseItem {\r\n  type: \"doc\" | \"video\" | \"quiz\" | \"module\";\r\n  label: string;\r\n  duration: string;\r\n}\r\n\r\ninterface CourseWeek {\r\n  title: string;\r\n  progress: number;\r\n  items: CourseItem[];\r\n}\r\n\r\ninterface CourseContent {\r\n  sections: number;\r\n  lectures: number;\r\n  hours: number;\r\n  weeks: CourseWeek[];\r\n}\r\n\r\ninterface Course {\r\n  title: string;\r\n  rating: number;\r\n  reviews: number;\r\n  description: string;\r\n  instructor: Instructor;\r\n  price: number;\r\n  oldPrice: number;\r\n  sale: number;\r\n  image: string;\r\n  includes: string[];\r\n  trial: boolean;\r\n  content: CourseContent;\r\n}\r\n\r\nconst mockCourseEn: Course = {\r\n  title: \"Beginner's Guide to Successful Company Management: Business and User Goals\",\r\n  rating: 4.9,\r\n  reviews: 236,\r\n  description:\r\n    \"Hello Student! 👋 Are you ready to embark on a comprehensive journey into the realm of successful company management with our Beginner's Guide course? During this course I'll help you delve deep into the intricacies of business strategy, organizational dynamics, and user-centric approaches. Ready to join? Send me a message and let's start! 🚀\",\r\n  instructor: {\r\n    name: \"<PERSON>\",\r\n    avatar: \"https://randomuser.me/api/portraits/women/44.jpg\",\r\n    stats: \"250+ students bought this course\",\r\n    recommend: \"98% students recommend this course\",\r\n  },\r\n  price: 87.99,\r\n  oldPrice: 183.0,\r\n  sale: 35,\r\n  image: \"https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80\",\r\n  includes: [\r\n    \"65 hours on demand video\",\r\n    \"45 downloadable resources\",\r\n    \"Access on mobile and TV\",\r\n    \"86 articles\",\r\n    \"30 min personal weekly session\",\r\n    \"Meeting with Oxford Professor\",\r\n    \"Certificate of completion\",\r\n  ],\r\n  trial: true,\r\n  content: {\r\n    sections: 24,\r\n    lectures: 490,\r\n    hours: 72,\r\n    weeks: [\r\n      {\r\n        title: \"Week 1 - Beginner - Introduction to Business Management\",\r\n        progress: 22,\r\n        items: [\r\n          { type: \"doc\", label: \"Read before you start\", duration: \"4min\" },\r\n          { type: \"video\", label: \"Introduction to Business Foundations & Principals of Management\", duration: \"1h 10min\" },\r\n          { type: \"video\", label: \"Introduction to Brand Management: Aligning Business, Brand and Behaviour\", duration: \"1h 23min\" },\r\n          { type: \"video\", label: \"Business Analysis & Process Management\", duration: \"43min\" },\r\n          { type: \"doc\", label: \"Major terms from the section\", duration: \"5 min\" },\r\n          { type: \"quiz\", label: \"Practice analyse\", duration: \"1 Question\" },\r\n          { type: \"module\", label: \"Module 1\", duration: \"24 Questions\" },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n};\r\n\r\nconst mockCourseAr: Course = {\r\n  title: \"دليل المبتدئين لإدارة الشركات الناجحة: أهداف العمل والمستخدم\",\r\n  rating: 4.9,\r\n  reviews: 236,\r\n  description:\r\n    \"مرحبًا أيها الطالب! 👋 هل أنت مستعد لبدء رحلة شاملة في عالم إدارة الشركات الناجحة مع دليل المبتدئين الخاص بنا؟ خلال هذه الدورة سأساعدك على التعمق في استراتيجيات الأعمال وديناميكيات التنظيم ونهج التركيز على المستخدم. مستعد للانضمام؟ أرسل لي رسالة ولنبدأ! 🚀\",\r\n  instructor: {\r\n    name: \"جيني ويلسون\",\r\n    avatar: \"https://randomuser.me/api/portraits/women/44.jpg\",\r\n    stats: \"أكثر من 250 طالب اشتروا هذه الدورة\",\r\n    recommend: \"98% من الطلاب يوصون بهذه الدورة\",\r\n  },\r\n  price: 87.99,\r\n  oldPrice: 183.0,\r\n  sale: 35,\r\n  image: \"https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80\",\r\n  includes: [\r\n    \"65 ساعة فيديو عند الطلب\",\r\n    \"45 موردًا قابلًا للتنزيل\",\r\n    \"الوصول عبر الجوال والتلفاز\",\r\n    \"86 مقالة\",\r\n    \"جلسة أسبوعية شخصية لمدة 30 دقيقة\",\r\n    \"اجتماع مع أستاذ من أكسفورد\",\r\n    \"شهادة إتمام\",\r\n  ],\r\n  trial: true,\r\n  content: {\r\n    sections: 24,\r\n    lectures: 490,\r\n    hours: 72,\r\n    weeks: [\r\n      {\r\n        title: \"الأسبوع 1 - المبتدئ - مقدمة في إدارة الأعمال\",\r\n        progress: 22,\r\n        items: [\r\n          { type: \"doc\", label: \"اقرأ قبل أن تبدأ\", duration: \"4 دقائق\" },\r\n          { type: \"video\", label: \"مقدمة في أسس الأعمال ومبادئ الإدارة\", duration: \"1س 10د\" },\r\n          { type: \"video\", label: \"مقدمة في إدارة العلامة التجارية: مواءمة الأعمال والعلامة والسلوك\", duration: \"1س 23د\" },\r\n          { type: \"video\", label: \"تحليل الأعمال وإدارة العمليات\", duration: \"43 دقيقة\" },\r\n          { type: \"doc\", label: \"المصطلحات الرئيسية من القسم\", duration: \"5 دقائق\" },\r\n          { type: \"quiz\", label: \"تحليل عملي\", duration: \"سؤال واحد\" },\r\n          { type: \"module\", label: \"الوحدة 1\", duration: \"24 سؤالاً\" },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n};\r\n\r\nconst iconMap: Record<string, any> = {\r\n  video: (\r\n    <svg className=\"w-5 h-5 text-blue-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z\" /></svg>\r\n  ),\r\n  doc: (\r\n    <svg className=\"w-5 h-5 text-green-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7 7h10M7 11h10M7 15h6\" /></svg>\r\n  ),\r\n  quiz: (\r\n    <svg className=\"w-5 h-5 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" /></svg>\r\n  ),\r\n  module: (\r\n    <svg className=\"w-5 h-5 text-purple-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"><circle cx=\"12\" cy=\"12\" r=\"10\" /><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 12h8\" /></svg>\r\n  ),\r\n};\r\n\r\n// Sidebar Navigation Component\r\nconst SidebarNav = () => {\r\n  const navItems = [\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\r\n        </svg>\r\n      ),\r\n      active: true\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n        </svg>\r\n      ),\r\n      active: false\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"fixed left-0 top-0 h-full w-16 bg-gray-900 flex flex-col items-center py-4 z-10\">\r\n      <div className=\"w-10 h-10 bg-white rounded-lg flex items-center justify-center mb-8\">\r\n        <svg className=\"w-6 h-6 text-gray-900\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path d=\"M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l5 6.625 5-6.625h-2.975c.257-3.351 3.06-6 6.475-6 3.584 0 6.5 2.916 6.5 6.5s-2.916 6.5-6.5 6.5c-1.863 0-3.542-.793-4.728-2.053l-2.427 3.216c1.877 1.754 4.389 2.837 7.155 2.837 5.79 0 10.5-4.71 10.5-10.5s-4.71-10.5-10.5-10.5z\"/>\r\n        </svg>\r\n      </div>\r\n      {navItems.map((item, index) => (\r\n        <div\r\n          key={index}\r\n          className={`w-10 h-10 rounded-lg flex items-center justify-center mb-2 cursor-pointer transition-colors ${\r\n            item.active ? 'bg-purple-600 text-white' : 'text-gray-400 hover:bg-gray-800'\r\n          }`}\r\n        >\r\n          {item.icon}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction CourseDetail() {\r\n  const [lang, setLang] = useState(\"en\");\r\n  const [expandedWeeks, setExpandedWeeks] = useState<number[]>([0]); // First week expanded by default\r\n  const [showPreview, setShowPreview] = useState(false);\r\n  const [completedItems, setCompletedItems] = useState<string[]>([]);\r\n  const course = lang === \"ar\" ? mockCourseAr : mockCourseEn;\r\n  const dir = lang === \"ar\" ? \"rtl\" : \"ltr\";\r\n\r\n  const toggleWeek = (weekIndex: number) => {\r\n    setExpandedWeeks(prev =>\r\n      prev.includes(weekIndex)\r\n        ? prev.filter(i => i !== weekIndex)\r\n        : [...prev, weekIndex]\r\n    );\r\n  };\r\n\r\n  const toggleItemCompletion = (itemId: string) => {\r\n    setCompletedItems(prev =>\r\n      prev.includes(itemId)\r\n        ? prev.filter(id => id !== itemId)\r\n        : [...prev, itemId]\r\n    );\r\n  };\r\n\r\n  // Text translations\r\n  const t = {\r\n    en: {\r\n      courses: \"Courses\",\r\n      popular: \"Popular courses\",\r\n      basedOn: \"based on\",\r\n      reviews: \"reviews\",\r\n      courseContent: \"Course content\",\r\n      sections: \"sections\",\r\n      lectures: \"lectures\",\r\n      hours: \"hours total length\",\r\n      expand: \"Expand all sections\",\r\n      buy: \"Buy course now\",\r\n      message: \"Send message to teacher\",\r\n      includes: \"This course includes\",\r\n      trial: \"10 min trial course\",\r\n      trialDesc: \"Have a look and feel at the course with a quick trial.\",\r\n      preview: \"Preview\",\r\n    },\r\n    ar: {\r\n      courses: \"الدورات\",\r\n      popular: \"الدورات الشائعة\",\r\n      basedOn: \"بناءً على\",\r\n      reviews: \"مراجعة\",\r\n      courseContent: \"محتوى الدورة\",\r\n      sections: \"أقسام\",\r\n      lectures: \"محاضرات\",\r\n      hours: \"إجمالي الساعات\",\r\n      expand: \"توسيع جميع الأقسام\",\r\n      buy: \"اشترِ الدورة الآن\",\r\n      message: \"أرسل رسالة للمدرس\",\r\n      includes: \"تشمل هذه الدورة\",\r\n      trial: \"دورة تجريبية لمدة 10 دقائق\",\r\n      trialDesc: \"اطلع على الدورة بسرعة.\",\r\n      preview: \"معاينة\",\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\" dir={dir}>\r\n      <SidebarNav />\r\n\r\n      {/* Main Content with left margin for sidebar */}\r\n      <div className=\"ml-16\">\r\n        {/* Header */}\r\n        <div className=\"bg-white border-b px-6 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  className=\"pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n                  placeholder={lang === 'ar' ? 'بحث' : 'Search'}\r\n                />\r\n                <svg className=\"w-4 h-4 text-gray-400 absolute left-3 top-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-4\">\r\n              <button className=\"p-2 text-gray-400 hover:text-gray-600\">\r\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM9 7H4l5-5v5z\" />\r\n                </svg>\r\n              </button>\r\n              <img src={course.instructor.avatar} alt=\"User\" className=\"w-8 h-8 rounded-full\" />\r\n              <button\r\n                className=\"px-3 py-1 rounded bg-purple-600 text-white text-xs font-semibold hover:bg-purple-700 transition\"\r\n                onClick={() => setLang(lang === \"en\" ? \"ar\" : \"en\")}\r\n              >\r\n                {lang === \"en\" ? \"العربية\" : \"English\"}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Breadcrumb */}\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"text-sm text-gray-500 flex items-center gap-2\">\r\n            <span>{t[lang].courses}</span>\r\n            <span>/</span>\r\n            <span>{t[lang].popular}</span>\r\n            <span>/</span>\r\n            <span className=\"font-medium text-gray-700\">{course.title}</span>\r\n          </div>\r\n        </div>\r\n        {/* Main Content */}\r\n        <div className=\"px-6 pb-6\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n            {/* Left/Main Content */}\r\n            <div className=\"lg:col-span-2\">\r\n              {/* Course Header */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-2 text-yellow-500 text-sm font-medium mb-3\">\r\n                  <span>★ {course.rating}</span>\r\n                  <span className=\"text-gray-500 text-sm font-normal\">{t[lang].basedOn} {course.reviews} {t[lang].reviews}</span>\r\n                  <button className=\"ml-auto bg-gray-100 hover:bg-gray-200 rounded-full p-2 text-gray-500\">\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n                <h1 className=\"text-3xl font-bold mb-4 text-gray-900 leading-tight\">\r\n                  {course.title}\r\n                </h1>\r\n                <p className=\"text-gray-600 text-base mb-6 leading-relaxed\">{course.description}</p>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <img src={course.instructor.avatar} alt=\"Instructor\" className=\"w-10 h-10 rounded-full\" />\r\n                  <div>\r\n                    <div className=\"font-medium text-gray-900 text-sm\">{course.instructor.name}</div>\r\n                    <div className=\"flex items-center gap-4 text-xs text-gray-500 mt-1\">\r\n                      <span>{course.instructor.stats}</span>\r\n                      <span>{course.instructor.recommend}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              {/* Course Content */}\r\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                  <h2 className=\"text-lg font-semibold text-gray-900\">{t[lang].courseContent}</h2>\r\n                  <button className=\"text-purple-600 text-sm font-medium hover:underline\">{t[lang].expand}</button>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-6 mb-6 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                    </svg>\r\n                    <span>{course.content.sections} {t[lang].sections}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z\" />\r\n                    </svg>\r\n                    <span>{course.content.lectures} {t[lang].lectures}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    <span>{course.content.hours} {t[lang].hours}</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Week Content */}\r\n                {course.content.weeks.map((week, i) => {\r\n                  const isExpanded = expandedWeeks.includes(i);\r\n                  const completedCount = week.items.filter(item =>\r\n                    completedItems.includes(`${i}-${item.label}`)\r\n                  ).length;\r\n                  const progressPercent = (completedCount / week.items.length) * 100;\r\n\r\n                  return (\r\n                    <div key={i} className=\"border border-gray-200 rounded-lg mb-4 overflow-hidden\">\r\n                      <button\r\n                        onClick={() => toggleWeek(i)}\r\n                        className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\r\n                      >\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <svg\r\n                            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${\r\n                              isExpanded ? 'rotate-180' : ''\r\n                            }`}\r\n                            fill=\"none\"\r\n                            stroke=\"currentColor\"\r\n                            viewBox=\"0 0 24 24\"\r\n                          >\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\r\n                          </svg>\r\n                          <span className=\"font-medium text-gray-900\">{week.title}</span>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <span className=\"text-xs text-purple-600 bg-purple-100 rounded-full px-2 py-1\">\r\n                              {Math.round(progressPercent)}%\r\n                            </span>\r\n                            <span className=\"text-xs text-gray-500\">\r\n                              {completedCount}/{week.items.length} completed\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"w-16 h-2 bg-gray-200 rounded-full overflow-hidden\">\r\n                          <div\r\n                            className=\"h-full bg-purple-600 transition-all duration-300\"\r\n                            style={{ width: `${progressPercent}%` }}\r\n                          />\r\n                        </div>\r\n                      </button>\r\n\r\n                      <div className={`transition-all duration-300 ease-in-out ${\r\n                        isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\r\n                      } overflow-hidden`}>\r\n                        <div className=\"p-4\">\r\n                          {week.items.map((item, j) => {\r\n                            const itemId = `${i}-${item.label}`;\r\n                            const isCompleted = completedItems.includes(itemId);\r\n\r\n                            return (\r\n                              <div\r\n                                key={j}\r\n                                className=\"flex items-center gap-3 py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded px-2 transition-colors group\"\r\n                              >\r\n                                <button\r\n                                  onClick={() => toggleItemCompletion(itemId)}\r\n                                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${\r\n                                    isCompleted\r\n                                      ? 'bg-green-500 border-green-500 text-white'\r\n                                      : 'border-gray-300 hover:border-green-400'\r\n                                  }`}\r\n                                >\r\n                                  {isCompleted && (\r\n                                    <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                                    </svg>\r\n                                  )}\r\n                                </button>\r\n                                {iconMap[item.type]}\r\n                                <span className={`text-sm flex-1 transition-colors ${\r\n                                  isCompleted ? 'text-gray-500 line-through' : 'text-gray-700'\r\n                                }`}>\r\n                                  {item.label}\r\n                                </span>\r\n                                <span className=\"text-xs text-gray-500\">{item.duration}</span>\r\n                                <button className=\"opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded\">\r\n                                  <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                                  </svg>\r\n                                </button>\r\n                              </div>\r\n                            );\r\n                          })}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n            {/* Sidebar */}\r\n            <aside className=\"lg:col-span-1\">\r\n              <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\r\n                {/* Course Image */}\r\n                <div className=\"relative\">\r\n                  <img src={course.image} alt=\"Course\" className=\"w-full h-48 object-cover\" />\r\n                  <div className=\"absolute top-4 right-4\">\r\n                    <button className=\"bg-white/90 backdrop-blur-sm rounded-full p-2 text-gray-600 hover:bg-white transition\">\r\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Course Info */}\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-center gap-3 mb-4\">\r\n                    <span className=\"text-3xl font-bold text-gray-900\">${course.price.toFixed(2)}</span>\r\n                    <span className=\"text-lg line-through text-gray-400\">${course.oldPrice.toFixed(2)}</span>\r\n                    <span className=\"text-xs bg-orange-100 text-orange-600 rounded-full px-2 py-1 font-medium\">{course.sale}% sale</span>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3 mb-6\">\r\n                    <button className=\"w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-lg transition flex items-center justify-center gap-2\">\r\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                      </svg>\r\n                      {t[lang].buy}\r\n                    </button>\r\n                    <button className=\"w-full border border-gray-200 hover:bg-gray-50 text-gray-700 font-medium py-3 rounded-lg transition flex items-center justify-center gap-2\">\r\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\r\n                      </svg>\r\n                      {t[lang].message}\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"mb-6\">\r\n                    <h3 className=\"font-semibold text-gray-900 text-sm mb-3\">{t[lang].includes}</h3>\r\n                    <ul className=\"space-y-2\">\r\n                      {course.includes.map((inc, i) => (\r\n                        <li key={i} className=\"flex items-center gap-3 text-sm text-gray-600\">\r\n                          <svg className=\"w-4 h-4 text-green-500 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M5 13l4 4L19 7\" />\r\n                          </svg>\r\n                          <span>{inc}</span>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n\r\n                  {course.trial && (\r\n                    <div className=\"bg-gray-900 rounded-lg p-4 text-center\">\r\n                      <h4 className=\"font-semibold text-white text-sm mb-2\">{t[lang].trial}</h4>\r\n                      <p className=\"text-gray-300 text-xs mb-3\">{t[lang].trialDesc}</p>\r\n                      <button className=\"w-full bg-white text-gray-900 rounded-lg py-2 text-sm font-medium hover:bg-gray-100 transition\">\r\n                        {t[lang].preview}\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </aside>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CourseDetail;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA0CA,MAAMC,YAAoB,GAAG;EAC3BC,KAAK,EAAE,4EAA4E;EACnFC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,GAAG;EACZC,WAAW,EACT,0VAA0V;EAC5VC,UAAU,EAAE;IACVC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,kDAAkD;IAC1DC,KAAK,EAAE,kCAAkC;IACzCC,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,8FAA8F;EACrGC,QAAQ,EAAE,CACR,0BAA0B,EAC1B,2BAA2B,EAC3B,yBAAyB,EACzB,aAAa,EACb,gCAAgC,EAChC,+BAA+B,EAC/B,2BAA2B,CAC5B;EACDC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE;IACPC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,CACL;MACEnB,KAAK,EAAE,yDAAyD;MAChEoB,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CACL;QAAEC,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,uBAAuB;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACjE;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,iEAAiE;QAAEC,QAAQ,EAAE;MAAW,CAAC,EACjH;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,0EAA0E;QAAEC,QAAQ,EAAE;MAAW,CAAC,EAC1H;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,wCAAwC;QAAEC,QAAQ,EAAE;MAAQ,CAAC,EACrF;QAAEF,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,8BAA8B;QAAEC,QAAQ,EAAE;MAAQ,CAAC,EACzE;QAAEF,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAa,CAAC,EACnE;QAAEF,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAe,CAAC;IAEnE,CAAC;EAEL;AACF,CAAC;AAED,MAAMC,YAAoB,GAAG;EAC3BzB,KAAK,EAAE,8DAA8D;EACrEC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,GAAG;EACZC,WAAW,EACT,kQAAkQ;EACpQC,UAAU,EAAE;IACVC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,kDAAkD;IAC1DC,KAAK,EAAE,oCAAoC;IAC3CC,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,8FAA8F;EACrGC,QAAQ,EAAE,CACR,yBAAyB,EACzB,0BAA0B,EAC1B,4BAA4B,EAC5B,UAAU,EACV,kCAAkC,EAClC,4BAA4B,EAC5B,aAAa,CACd;EACDC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE;IACPC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,CACL;MACEnB,KAAK,EAAE,8CAA8C;MACrDoB,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CACL;QAAEC,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAU,CAAC,EAC/D;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,qCAAqC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EACnF;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,kEAAkE;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAChH;QAAEF,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,+BAA+B;QAAEC,QAAQ,EAAE;MAAW,CAAC,EAC/E;QAAEF,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,6BAA6B;QAAEC,QAAQ,EAAE;MAAU,CAAC,EAC1E;QAAEF,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAY,CAAC,EAC5D;QAAEF,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAY,CAAC;IAEhE,CAAC;EAEL;AACF,CAAC;AAED,MAAME,OAA4B,GAAG;EACnCC,KAAK,eACH7B,OAAA;IAAK8B,SAAS,EAAC,uBAAuB;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,WAAW,EAAC,GAAG;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAACnC,OAAA;MAAMoC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,CAAC,EAAC;IAAmI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAC9S;EACDC,GAAG,eACD3C,OAAA;IAAK8B,SAAS,EAAC,wBAAwB;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,WAAW,EAAC,GAAG;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAACnC,OAAA;MAAMoC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,CAAC,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CACpM;EACDE,IAAI,eACF5C,OAAA;IAAK8B,SAAS,EAAC,yBAAyB;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,WAAW,EAAC,GAAG;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,eAACnC,OAAA;MAAMoC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,CAAC,EAAC;IAA6C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAC1N;EACDG,MAAM,eACJ7C,OAAA;IAAK8B,SAAS,EAAC,yBAAyB;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAACC,WAAW,EAAC,GAAG;IAACC,OAAO,EAAC,WAAW;IAAAC,QAAA,gBAACnC,OAAA;MAAQ8C,EAAE,EAAC,IAAI;MAACC,EAAE,EAAC,IAAI;MAACC,CAAC,EAAC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAAA1C,OAAA;MAAMoC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,CAAC,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK;AAE1N,CAAC;;AAED;AACA,MAAMO,UAAU,GAAGA,CAAA,KAAM;EACvB,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtI,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAsM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3Q,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAsH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3L,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9L,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAA+J;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpO,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrG,CACN;IACDU,MAAM,EAAE;EACV,CAAC,EACD;IACED,IAAI,eACFnD,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACE,OAAO,EAAC,WAAW;MAAAC,QAAA,gBAC5EnC,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAqe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7iB1C,OAAA;QAAMoC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACJ,WAAW,EAAC,GAAG;QAACK,CAAC,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CACN;IACDU,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEpD,OAAA;IAAK8B,SAAS,EAAC,iFAAiF;IAAAK,QAAA,gBAC9FnC,OAAA;MAAK8B,SAAS,EAAC,qEAAqE;MAAAK,QAAA,eAClFnC,OAAA;QAAK8B,SAAS,EAAC,uBAAuB;QAACC,IAAI,EAAC,cAAc;QAACG,OAAO,EAAC,WAAW;QAAAC,QAAA,eAC5EnC,OAAA;UAAMsC,CAAC,EAAC;QAAgR;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLQ,QAAQ,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBvD,OAAA;MAEE8B,SAAS,EAAE,+FACTwB,IAAI,CAACF,MAAM,GAAG,0BAA0B,GAAG,iCAAiC,EAC3E;MAAAjB,QAAA,EAEFmB,IAAI,CAACH;IAAI,GALLI,KAAK;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMP,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACc,EAAA,GAhFIP,UAAU;AAkFhB,SAASQ,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAMqE,MAAM,GAAGR,IAAI,KAAK,IAAI,GAAGhC,YAAY,GAAG1B,YAAY;EAC1D,MAAMmE,GAAG,GAAGT,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;EAEzC,MAAMU,UAAU,GAAIC,SAAiB,IAAK;IACxCR,gBAAgB,CAACS,IAAI,IACnBA,IAAI,CAACxD,QAAQ,CAACuD,SAAS,CAAC,GACpBC,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,SAAS,CAAC,GACjC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMI,oBAAoB,GAAIC,MAAc,IAAK;IAC/CT,iBAAiB,CAACK,IAAI,IACpBA,IAAI,CAACxD,QAAQ,CAAC4D,MAAM,CAAC,GACjBJ,IAAI,CAACC,MAAM,CAACI,EAAE,IAAIA,EAAE,KAAKD,MAAM,CAAC,GAChC,CAAC,GAAGJ,IAAI,EAAEI,MAAM,CACtB,CAAC;EACH,CAAC;;EAED;EACA,MAAME,CAAC,GAAG;IACRC,EAAE,EAAE;MACFC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,UAAU;MACnB7E,OAAO,EAAE,SAAS;MAClB8E,aAAa,EAAE,gBAAgB;MAC/BhE,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,oBAAoB;MAC3B+D,MAAM,EAAE,qBAAqB;MAC7BC,GAAG,EAAE,gBAAgB;MACrBC,OAAO,EAAE,yBAAyB;MAClCtE,QAAQ,EAAE,sBAAsB;MAChCC,KAAK,EAAE,qBAAqB;MAC5BsE,SAAS,EAAE,wDAAwD;MACnEC,OAAO,EAAE;IACX,CAAC;IACDC,EAAE,EAAE;MACFT,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,WAAW;MACpB7E,OAAO,EAAE,QAAQ;MACjB8E,aAAa,EAAE,cAAc;MAC7BhE,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,gBAAgB;MACvB+D,MAAM,EAAE,oBAAoB;MAC5BC,GAAG,EAAE,mBAAmB;MACxBC,OAAO,EAAE,mBAAmB;MAC5BtE,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,4BAA4B;MACnCsE,SAAS,EAAE,wBAAwB;MACnCC,OAAO,EAAE;IACX;EACF,CAAC;EAED,oBACEvF,OAAA;IAAK8B,SAAS,EAAC,yBAAyB;IAACsC,GAAG,EAAEA,GAAI;IAAAjC,QAAA,gBAChDnC,OAAA,CAACiD,UAAU;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGd1C,OAAA;MAAK8B,SAAS,EAAC,OAAO;MAAAK,QAAA,gBAEpBnC,OAAA;QAAK8B,SAAS,EAAC,6BAA6B;QAAAK,QAAA,eAC1CnC,OAAA;UAAK8B,SAAS,EAAC,mCAAmC;UAAAK,QAAA,gBAChDnC,OAAA;YAAK8B,SAAS,EAAC,yBAAyB;YAAAK,QAAA,eACtCnC,OAAA;cAAK8B,SAAS,EAAC,UAAU;cAAAK,QAAA,gBACvBnC,OAAA;gBACE8B,SAAS,EAAC,sHAAsH;gBAChI2D,WAAW,EAAE9B,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG;cAAS;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACF1C,OAAA;gBAAK8B,SAAS,EAAC,6CAA6C;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACE,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAChHnC,OAAA;kBAAMoC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACJ,WAAW,EAAC,GAAG;kBAACK,CAAC,EAAC;gBAA6C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1C,OAAA;YAAK8B,SAAS,EAAC,yBAAyB;YAAAK,QAAA,gBACtCnC,OAAA;cAAQ8B,SAAS,EAAC,uCAAuC;cAAAK,QAAA,eACvDnC,OAAA;gBAAK8B,SAAS,EAAC,SAAS;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACE,OAAO,EAAC,WAAW;gBAAAC,QAAA,eAC5EnC,OAAA;kBAAMoC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACJ,WAAW,EAAC,GAAG;kBAACK,CAAC,EAAC;gBAAgC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACT1C,OAAA;cAAK0F,GAAG,EAAEvB,MAAM,CAAC7D,UAAU,CAACE,MAAO;cAACmF,GAAG,EAAC,MAAM;cAAC7D,SAAS,EAAC;YAAsB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF1C,OAAA;cACE8B,SAAS,EAAC,iGAAiG;cAC3G8D,OAAO,EAAEA,CAAA,KAAMhC,OAAO,CAACD,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;cAAAxB,QAAA,EAEnDwB,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG;YAAS;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAK,QAAA,eACxBnC,OAAA;UAAK8B,SAAS,EAAC,+CAA+C;UAAAK,QAAA,gBAC5DnC,OAAA;YAAAmC,QAAA,EAAO0C,CAAC,CAAClB,IAAI,CAAC,CAACoB;UAAO;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9B1C,OAAA;YAAAmC,QAAA,EAAM;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd1C,OAAA;YAAAmC,QAAA,EAAO0C,CAAC,CAAClB,IAAI,CAAC,CAACqB;UAAO;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9B1C,OAAA;YAAAmC,QAAA,EAAM;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd1C,OAAA;YAAM8B,SAAS,EAAC,2BAA2B;YAAAK,QAAA,EAAEgC,MAAM,CAACjE;UAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1C,OAAA;QAAK8B,SAAS,EAAC,WAAW;QAAAK,QAAA,eACxBnC,OAAA;UAAK8B,SAAS,EAAC,uCAAuC;UAAAK,QAAA,gBAEpDnC,OAAA;YAAK8B,SAAS,EAAC,eAAe;YAAAK,QAAA,gBAE5BnC,OAAA;cAAK8B,SAAS,EAAC,MAAM;cAAAK,QAAA,gBACnBnC,OAAA;gBAAK8B,SAAS,EAAC,kEAAkE;gBAAAK,QAAA,gBAC/EnC,OAAA;kBAAAmC,QAAA,GAAM,SAAE,EAACgC,MAAM,CAAChE,MAAM;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9B1C,OAAA;kBAAM8B,SAAS,EAAC,mCAAmC;kBAAAK,QAAA,GAAE0C,CAAC,CAAClB,IAAI,CAAC,CAACsB,OAAO,EAAC,GAAC,EAACd,MAAM,CAAC/D,OAAO,EAAC,GAAC,EAACyE,CAAC,CAAClB,IAAI,CAAC,CAACvD,OAAO;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/G1C,OAAA;kBAAQ8B,SAAS,EAAC,sEAAsE;kBAAAK,QAAA,eACtFnC,OAAA;oBAAK8B,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAC,QAAA,eAC5FnC,OAAA;sBAAMoC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAmD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN1C,OAAA;gBAAI8B,SAAS,EAAC,qDAAqD;gBAAAK,QAAA,EAChEgC,MAAM,CAACjE;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACL1C,OAAA;gBAAG8B,SAAS,EAAC,8CAA8C;gBAAAK,QAAA,EAAEgC,MAAM,CAAC9D;cAAW;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpF1C,OAAA;gBAAK8B,SAAS,EAAC,yBAAyB;gBAAAK,QAAA,gBACtCnC,OAAA;kBAAK0F,GAAG,EAAEvB,MAAM,CAAC7D,UAAU,CAACE,MAAO;kBAACmF,GAAG,EAAC,YAAY;kBAAC7D,SAAS,EAAC;gBAAwB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1F1C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAK8B,SAAS,EAAC,mCAAmC;oBAAAK,QAAA,EAAEgC,MAAM,CAAC7D,UAAU,CAACC;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjF1C,OAAA;oBAAK8B,SAAS,EAAC,oDAAoD;oBAAAK,QAAA,gBACjEnC,OAAA;sBAAAmC,QAAA,EAAOgC,MAAM,CAAC7D,UAAU,CAACG;oBAAK;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtC1C,OAAA;sBAAAmC,QAAA,EAAOgC,MAAM,CAAC7D,UAAU,CAACI;oBAAS;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAK8B,SAAS,EAAC,gDAAgD;cAAAK,QAAA,gBAC7DnC,OAAA;gBAAK8B,SAAS,EAAC,wCAAwC;gBAAAK,QAAA,gBACrDnC,OAAA;kBAAI8B,SAAS,EAAC,qCAAqC;kBAAAK,QAAA,EAAE0C,CAAC,CAAClB,IAAI,CAAC,CAACuB;gBAAa;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChF1C,OAAA;kBAAQ8B,SAAS,EAAC,qDAAqD;kBAAAK,QAAA,EAAE0C,CAAC,CAAClB,IAAI,CAAC,CAACwB;gBAAM;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC,eAEN1C,OAAA;gBAAK8B,SAAS,EAAC,oDAAoD;gBAAAK,QAAA,gBACjEnC,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAK,QAAA,gBACtCnC,OAAA;oBAAK8B,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACE,OAAO,EAAC,WAAW;oBAAAC,QAAA,eAC5EnC,OAAA;sBAAMoC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,WAAW,EAAC,GAAG;sBAACK,CAAC,EAAC;oBAAsH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3L,CAAC,eACN1C,OAAA;oBAAAmC,QAAA,GAAOgC,MAAM,CAAClD,OAAO,CAACC,QAAQ,EAAC,GAAC,EAAC2D,CAAC,CAAClB,IAAI,CAAC,CAACzC,QAAQ;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN1C,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAK,QAAA,gBACtCnC,OAAA;oBAAK8B,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACE,OAAO,EAAC,WAAW;oBAAAC,QAAA,eAC5EnC,OAAA;sBAAMoC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,WAAW,EAAC,GAAG;sBAACK,CAAC,EAAC;oBAAmI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxM,CAAC,eACN1C,OAAA;oBAAAmC,QAAA,GAAOgC,MAAM,CAAClD,OAAO,CAACE,QAAQ,EAAC,GAAC,EAAC0D,CAAC,CAAClB,IAAI,CAAC,CAACxC,QAAQ;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN1C,OAAA;kBAAK8B,SAAS,EAAC,yBAAyB;kBAAAK,QAAA,gBACtCnC,OAAA;oBAAK8B,SAAS,EAAC,SAAS;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACE,OAAO,EAAC,WAAW;oBAAAC,QAAA,eAC5EnC,OAAA;sBAAMoC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACJ,WAAW,EAAC,GAAG;sBAACK,CAAC,EAAC;oBAA6C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClH,CAAC,eACN1C,OAAA;oBAAAmC,QAAA,GAAOgC,MAAM,CAAClD,OAAO,CAACG,KAAK,EAAC,GAAC,EAACyD,CAAC,CAAClB,IAAI,CAAC,CAACvC,KAAK;kBAAA;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLyB,MAAM,CAAClD,OAAO,CAACI,KAAK,CAACgC,GAAG,CAAC,CAACwC,IAAI,EAAEpB,CAAC,KAAK;gBACrC,MAAMqB,UAAU,GAAGjC,aAAa,CAAC9C,QAAQ,CAAC0D,CAAC,CAAC;gBAC5C,MAAMsB,cAAc,GAAGF,IAAI,CAACtE,KAAK,CAACiD,MAAM,CAAClB,IAAI,IAC3CW,cAAc,CAAClD,QAAQ,CAAC,GAAG0D,CAAC,IAAInB,IAAI,CAAC7B,KAAK,EAAE,CAC9C,CAAC,CAACuE,MAAM;gBACR,MAAMC,eAAe,GAAIF,cAAc,GAAGF,IAAI,CAACtE,KAAK,CAACyE,MAAM,GAAI,GAAG;gBAElE,oBACEhG,OAAA;kBAAa8B,SAAS,EAAC,wDAAwD;kBAAAK,QAAA,gBAC7EnC,OAAA;oBACE4F,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAACI,CAAC,CAAE;oBAC7B3C,SAAS,EAAC,6FAA6F;oBAAAK,QAAA,gBAEvGnC,OAAA;sBAAK8B,SAAS,EAAC,yBAAyB;sBAAAK,QAAA,gBACtCnC,OAAA;wBACE8B,SAAS,EAAE,2DACTgE,UAAU,GAAG,YAAY,GAAG,EAAE,EAC7B;wBACH/D,IAAI,EAAC,MAAM;wBACXC,MAAM,EAAC,cAAc;wBACrBE,OAAO,EAAC,WAAW;wBAAAC,QAAA,eAEnBnC,OAAA;0BAAMoC,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACJ,WAAW,EAAC,GAAG;0BAACK,CAAC,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC,eACN1C,OAAA;wBAAM8B,SAAS,EAAC,2BAA2B;wBAAAK,QAAA,EAAE0D,IAAI,CAAC3F;sBAAK;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/D1C,OAAA;wBAAK8B,SAAS,EAAC,yBAAyB;wBAAAK,QAAA,gBACtCnC,OAAA;0BAAM8B,SAAS,EAAC,8DAA8D;0BAAAK,QAAA,GAC3E+D,IAAI,CAACC,KAAK,CAACF,eAAe,CAAC,EAAC,GAC/B;wBAAA;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACP1C,OAAA;0BAAM8B,SAAS,EAAC,uBAAuB;0BAAAK,QAAA,GACpC4D,cAAc,EAAC,GAAC,EAACF,IAAI,CAACtE,KAAK,CAACyE,MAAM,EAAC,YACtC;wBAAA;0BAAAzD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1C,OAAA;sBAAK8B,SAAS,EAAC,mDAAmD;sBAAAK,QAAA,eAChEnC,OAAA;wBACE8B,SAAS,EAAC,kDAAkD;wBAC5DsE,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGJ,eAAe;wBAAI;sBAAE;wBAAA1D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eAET1C,OAAA;oBAAK8B,SAAS,EAAE,2CACdgE,UAAU,GAAG,sBAAsB,GAAG,mBAAmB,kBACxC;oBAAA3D,QAAA,eACjBnC,OAAA;sBAAK8B,SAAS,EAAC,KAAK;sBAAAK,QAAA,EACjB0D,IAAI,CAACtE,KAAK,CAAC8B,GAAG,CAAC,CAACC,IAAI,EAAEgD,CAAC,KAAK;wBAC3B,MAAM3B,MAAM,GAAG,GAAGF,CAAC,IAAInB,IAAI,CAAC7B,KAAK,EAAE;wBACnC,MAAM8E,WAAW,GAAGtC,cAAc,CAAClD,QAAQ,CAAC4D,MAAM,CAAC;wBAEnD,oBACE3E,OAAA;0BAEE8B,SAAS,EAAC,6HAA6H;0BAAAK,QAAA,gBAEvInC,OAAA;4BACE4F,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACC,MAAM,CAAE;4BAC5C7C,SAAS,EAAE,oFACTyE,WAAW,GACP,0CAA0C,GAC1C,wCAAwC,EAC3C;4BAAApE,QAAA,EAEFoE,WAAW,iBACVvG,OAAA;8BAAK8B,SAAS,EAAC,SAAS;8BAACC,IAAI,EAAC,MAAM;8BAACC,MAAM,EAAC,cAAc;8BAACE,OAAO,EAAC,WAAW;8BAAAC,QAAA,eAC5EnC,OAAA;gCAAMoC,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACJ,WAAW,EAAC,GAAG;gCAACK,CAAC,EAAC;8BAAgB;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrF;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACK,CAAC,EACRd,OAAO,CAAC0B,IAAI,CAAC9B,IAAI,CAAC,eACnBxB,OAAA;4BAAM8B,SAAS,EAAE,oCACfyE,WAAW,GAAG,4BAA4B,GAAG,eAAe,EAC3D;4BAAApE,QAAA,EACAmB,IAAI,CAAC7B;0BAAK;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC,eACP1C,OAAA;4BAAM8B,SAAS,EAAC,uBAAuB;4BAAAK,QAAA,EAAEmB,IAAI,CAAC5B;0BAAQ;4BAAAa,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAC9D1C,OAAA;4BAAQ8B,SAAS,EAAC,oFAAoF;4BAAAK,QAAA,eACpGnC,OAAA;8BAAK8B,SAAS,EAAC,uBAAuB;8BAACC,IAAI,EAAC,MAAM;8BAACC,MAAM,EAAC,cAAc;8BAACE,OAAO,EAAC,WAAW;8BAAAC,QAAA,eAC1FnC,OAAA;gCAAMoC,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACJ,WAAW,EAAC,GAAG;gCAACK,CAAC,EAAC;8BAAmG;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxK;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,GA5BJ4D,CAAC;0BAAA/D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA6BH,CAAC;sBAEV,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA7EE+B,CAAC;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8EN,CAAC;cAEV,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1C,OAAA;YAAO8B,SAAS,EAAC,eAAe;YAAAK,QAAA,eAC9BnC,OAAA;cAAK8B,SAAS,EAAC,4DAA4D;cAAAK,QAAA,gBAEzEnC,OAAA;gBAAK8B,SAAS,EAAC,UAAU;gBAAAK,QAAA,gBACvBnC,OAAA;kBAAK0F,GAAG,EAAEvB,MAAM,CAACrD,KAAM;kBAAC6E,GAAG,EAAC,QAAQ;kBAAC7D,SAAS,EAAC;gBAA0B;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5E1C,OAAA;kBAAK8B,SAAS,EAAC,wBAAwB;kBAAAK,QAAA,eACrCnC,OAAA;oBAAQ8B,SAAS,EAAC,uFAAuF;oBAAAK,QAAA,eACvGnC,OAAA;sBAAK8B,SAAS,EAAC,SAAS;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACE,OAAO,EAAC,WAAW;sBAAAC,QAAA,eAC5EnC,OAAA;wBAAMoC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACJ,WAAW,EAAC,GAAG;wBAACK,CAAC,EAAC;sBAAmD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1C,OAAA;gBAAK8B,SAAS,EAAC,KAAK;gBAAAK,QAAA,gBAClBnC,OAAA;kBAAK8B,SAAS,EAAC,8BAA8B;kBAAAK,QAAA,gBAC3CnC,OAAA;oBAAM8B,SAAS,EAAC,kCAAkC;oBAAAK,QAAA,GAAC,GAAC,EAACgC,MAAM,CAACxD,KAAK,CAAC6F,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpF1C,OAAA;oBAAM8B,SAAS,EAAC,oCAAoC;oBAAAK,QAAA,GAAC,GAAC,EAACgC,MAAM,CAACvD,QAAQ,CAAC4F,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzF1C,OAAA;oBAAM8B,SAAS,EAAC,0EAA0E;oBAAAK,QAAA,GAAEgC,MAAM,CAACtD,IAAI,EAAC,QAAM;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC,eAEN1C,OAAA;kBAAK8B,SAAS,EAAC,gBAAgB;kBAAAK,QAAA,gBAC7BnC,OAAA;oBAAQ8B,SAAS,EAAC,qIAAqI;oBAAAK,QAAA,gBACrJnC,OAAA;sBAAK8B,SAAS,EAAC,SAAS;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACE,OAAO,EAAC,WAAW;sBAAAC,QAAA,eAC5EnC,OAAA;wBAAMoC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACJ,WAAW,EAAC,GAAG;wBAACK,CAAC,EAAC;sBAA4B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjG,CAAC,EACLmC,CAAC,CAAClB,IAAI,CAAC,CAACyB,GAAG;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACT1C,OAAA;oBAAQ8B,SAAS,EAAC,4IAA4I;oBAAAK,QAAA,gBAC5JnC,OAAA;sBAAK8B,SAAS,EAAC,SAAS;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACE,OAAO,EAAC,WAAW;sBAAAC,QAAA,eAC5EnC,OAAA;wBAAMoC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACJ,WAAW,EAAC,GAAG;wBAACK,CAAC,EAAC;sBAA+J;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpO,CAAC,EACLmC,CAAC,CAAClB,IAAI,CAAC,CAAC0B,OAAO;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN1C,OAAA;kBAAK8B,SAAS,EAAC,MAAM;kBAAAK,QAAA,gBACnBnC,OAAA;oBAAI8B,SAAS,EAAC,0CAA0C;oBAAAK,QAAA,EAAE0C,CAAC,CAAClB,IAAI,CAAC,CAAC5C;kBAAQ;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChF1C,OAAA;oBAAI8B,SAAS,EAAC,WAAW;oBAAAK,QAAA,EACtBgC,MAAM,CAACpD,QAAQ,CAACsC,GAAG,CAAC,CAACoD,GAAG,EAAEhC,CAAC,kBAC1BzE,OAAA;sBAAY8B,SAAS,EAAC,+CAA+C;sBAAAK,QAAA,gBACnEnC,OAAA;wBAAK8B,SAAS,EAAC,sCAAsC;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC,GAAG;wBAACC,OAAO,EAAC,WAAW;wBAAAC,QAAA,eACzHnC,OAAA;0BAAMoC,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,eACN1C,OAAA;wBAAAmC,QAAA,EAAOsE;sBAAG;wBAAAlE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAJX+B,CAAC;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKN,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAELyB,MAAM,CAACnD,KAAK,iBACXhB,OAAA;kBAAK8B,SAAS,EAAC,wCAAwC;kBAAAK,QAAA,gBACrDnC,OAAA;oBAAI8B,SAAS,EAAC,uCAAuC;oBAAAK,QAAA,EAAE0C,CAAC,CAAClB,IAAI,CAAC,CAAC3C;kBAAK;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1E1C,OAAA;oBAAG8B,SAAS,EAAC,4BAA4B;oBAAAK,QAAA,EAAE0C,CAAC,CAAClB,IAAI,CAAC,CAAC2B;kBAAS;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE1C,OAAA;oBAAQ8B,SAAS,EAAC,gGAAgG;oBAAAK,QAAA,EAC/G0C,CAAC,CAAClB,IAAI,CAAC,CAAC4B;kBAAO;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACgB,EAAA,CAzUQD,YAAY;AAAAiD,GAAA,GAAZjD,YAAY;AA2UrB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAkD,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}