{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\CourseBuilder.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CourseBuilder = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [courseTitle, setCourseTitle] = useState(\"\");\n  const [courseDescription, setCourseDescription] = useState(\"\");\n  const [coursePrice, setCoursePrice] = useState(0);\n  const steps = [{\n    id: 1,\n    title: \"Basic Information\",\n    icon: \"📝\"\n  }, {\n    id: 2,\n    title: \"Course Content\",\n    icon: \"📚\"\n  }, {\n    id: 3,\n    title: \"Learning Objectives\",\n    icon: \"🎯\"\n  }, {\n    id: 4,\n    title: \"Pricing & Publishing\",\n    icon: \"💰\"\n  }, {\n    id: 5,\n    title: \"Preview & Publish\",\n    icon: \"🚀\"\n  }];\n  const nextStep = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Course Builder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm\",\n            children: \"Create and manage your online course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition\",\n            children: \"Save Draft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition\",\n            children: \"Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\",\n            children: \"Publish Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-64 bg-white border-r min-h-screen p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: steps.map(step => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentStep(step.id),\n            className: `w-full flex items-center gap-3 p-3 rounded-lg text-left transition ${currentStep === step.id ? \"bg-purple-100 text-purple-700 border border-purple-200\" : \"text-gray-600 hover:bg-gray-50\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: step.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: step.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: [\"Step \", step.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), currentStep === step.id && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-auto w-2 h-2 bg-purple-600 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 19\n            }, this)]\n          }, step.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-6\",\n        children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Basic Course Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Provide the essential details about your course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Course Title *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: courseTitle,\n                  onChange: e => setCourseTitle(e.target.value),\n                  placeholder: \"Enter your course title\",\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Course Description *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: courseDescription,\n                  onChange: e => setCourseDescription(e.target.value),\n                  placeholder: \"Describe what students will learn in this course\",\n                  rows: 4,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Course Price (USD)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute left-3 top-2 text-gray-500\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: coursePrice,\n                    onChange: e => setCoursePrice(parseFloat(e.target.value) || 0),\n                    placeholder: \"0.00\",\n                    className: \"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), currentStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Course Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Organize your course into sections and lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Add Your First Section\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"Start building your course by adding sections and lessons\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\",\n                children: \"Add Section\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), currentStep === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Learning Objectives\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Define what students will learn and achieve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Define Learning Objectives\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"What will students be able to do after completing your course?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\",\n                children: \"Add Objective\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Pricing & Publishing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Set your course price and publishing options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Course Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: [\"Current price: $\", coursePrice.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\",\n                children: \"Update Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), currentStep === 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Preview & Publish\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Review your course before publishing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Ready to Publish\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: [\"Course: \", courseTitle || \"Untitled Course\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-6 py-3 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition\",\n                  children: \"Preview Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\",\n                  children: \"Publish Course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-8 pt-6 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: prevStep,\n            disabled: currentStep === 1,\n            className: `px-6 py-2 rounded-lg transition ${currentStep === 1 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"}`,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition\",\n              children: \"Save Draft\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: nextStep,\n              disabled: currentStep === steps.length,\n              className: `px-6 py-2 rounded-lg transition ${currentStep === steps.length ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-purple-600 text-white hover:bg-purple-700\"}`,\n              children: currentStep === steps.length ? \"Completed\" : \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseBuilder, \"g00tkZG4XOloDdbpnWuePR82J/8=\");\n_c = CourseBuilder;\nexport default CourseBuilder;\nvar _c;\n$RefreshReg$(_c, \"CourseBuilder\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CourseBuilder", "_s", "currentStep", "setCurrentStep", "courseTitle", "setCourseTitle", "courseDescription", "setCourseDescription", "coursePrice", "setCoursePrice", "steps", "id", "title", "icon", "nextStep", "length", "prevStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "rows", "parseFloat", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "toFixed", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/CourseBuilder.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst CourseBuilder: React.FC = () => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [courseTitle, setCourseTitle] = useState(\"\");\n  const [courseDescription, setCourseDescription] = useState(\"\");\n  const [coursePrice, setCoursePrice] = useState(0);\n\n  const steps = [\n    { id: 1, title: \"Basic Information\", icon: \"📝\" },\n    { id: 2, title: \"Course Content\", icon: \"📚\" },\n    { id: 3, title: \"Learning Objectives\", icon: \"🎯\" },\n    { id: 4, title: \"Pricing & Publishing\", icon: \"💰\" },\n    { id: 5, title: \"Preview & Publish\", icon: \"🚀\" },\n  ];\n\n  const nextStep = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Course Builder</h1>\n            <p className=\"text-gray-600 text-sm\">Create and manage your online course</p>\n          </div>\n          <div className=\"flex items-center gap-3\">\n            <button className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition\">\n              Save Draft\n            </button>\n            <button className=\"px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition\">\n              Preview\n            </button>\n            <button className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\">\n              Publish Course\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        <div className=\"w-64 bg-white border-r min-h-screen p-6\">\n          <div className=\"space-y-2\">\n            {steps.map((step) => (\n              <button\n                key={step.id}\n                onClick={() => setCurrentStep(step.id)}\n                className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition ${\n                  currentStep === step.id\n                    ? \"bg-purple-100 text-purple-700 border border-purple-200\"\n                    : \"text-gray-600 hover:bg-gray-50\"\n                }`}\n              >\n                <span className=\"text-lg\">{step.icon}</span>\n                <div>\n                  <div className=\"font-medium\">{step.title}</div>\n                  <div className=\"text-xs text-gray-500\">Step {step.id}</div>\n                </div>\n                {currentStep === step.id && (\n                  <div className=\"ml-auto w-2 h-2 bg-purple-600 rounded-full\"></div>\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1 p-6\">\n          {/* Step 1: Basic Information */}\n          {currentStep === 1 && (\n            <div className=\"max-w-4xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Basic Course Information</h2>\n                <p className=\"text-gray-600\">Provide the essential details about your course</p>\n              </div>\n\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                <div className=\"space-y-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Course Title *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={courseTitle}\n                      onChange={(e) => setCourseTitle(e.target.value)}\n                      placeholder=\"Enter your course title\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Course Description *\n                    </label>\n                    <textarea\n                      value={courseDescription}\n                      onChange={(e) => setCourseDescription(e.target.value)}\n                      placeholder=\"Describe what students will learn in this course\"\n                      rows={4}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Course Price (USD)\n                    </label>\n                    <div className=\"relative\">\n                      <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                      <input\n                        type=\"number\"\n                        value={coursePrice}\n                        onChange={(e) => setCoursePrice(parseFloat(e.target.value) || 0)}\n                        placeholder=\"0.00\"\n                        className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Step 2: Course Content */}\n          {currentStep === 2 && (\n            <div className=\"max-w-6xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Course Content</h2>\n                <p className=\"text-gray-600\">Organize your course into sections and lessons</p>\n              </div>\n\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                <div className=\"text-center py-12\">\n                  <svg className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                  </svg>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Add Your First Section</h3>\n                  <p className=\"text-gray-600 mb-4\">Start building your course by adding sections and lessons</p>\n                  <button className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\">\n                    Add Section\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Step 3: Learning Objectives */}\n          {currentStep === 3 && (\n            <div className=\"max-w-4xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Learning Objectives</h2>\n                <p className=\"text-gray-600\">Define what students will learn and achieve</p>\n              </div>\n\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                <div className=\"text-center py-12\">\n                  <svg className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Define Learning Objectives</h3>\n                  <p className=\"text-gray-600 mb-4\">What will students be able to do after completing your course?</p>\n                  <button className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\">\n                    Add Objective\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Step 4: Pricing & Publishing */}\n          {currentStep === 4 && (\n            <div className=\"max-w-4xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Pricing & Publishing</h2>\n                <p className=\"text-gray-600\">Set your course price and publishing options</p>\n              </div>\n\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                <div className=\"text-center py-12\">\n                  <svg className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Course Pricing</h3>\n                  <p className=\"text-gray-600 mb-4\">Current price: ${coursePrice.toFixed(2)}</p>\n                  <button className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\">\n                    Update Pricing\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Step 5: Preview & Publish */}\n          {currentStep === 5 && (\n            <div className=\"max-w-4xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Preview & Publish</h2>\n                <p className=\"text-gray-600\">Review your course before publishing</p>\n              </div>\n\n              <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                <div className=\"text-center py-12\">\n                  <svg className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                  </svg>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Publish</h3>\n                  <p className=\"text-gray-600 mb-4\">Course: {courseTitle || \"Untitled Course\"}</p>\n                  <div className=\"flex items-center justify-center gap-4\">\n                    <button className=\"px-6 py-3 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition\">\n                      Preview Course\n                    </button>\n                    <button className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition\">\n                      Publish Course\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Navigation Buttons */}\n          <div className=\"flex items-center justify-between mt-8 pt-6 border-t\">\n            <button\n              onClick={prevStep}\n              disabled={currentStep === 1}\n              className={`px-6 py-2 rounded-lg transition ${\n                currentStep === 1\n                  ? \"bg-gray-100 text-gray-400 cursor-not-allowed\"\n                  : \"bg-gray-200 text-gray-700 hover:bg-gray-300\"\n              }`}\n            >\n              Previous\n            </button>\n            <div className=\"flex items-center gap-3\">\n              <button className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition\">\n                Save Draft\n              </button>\n              <button\n                onClick={nextStep}\n                disabled={currentStep === steps.length}\n                className={`px-6 py-2 rounded-lg transition ${\n                  currentStep === steps.length\n                    ? \"bg-gray-100 text-gray-400 cursor-not-allowed\"\n                    : \"bg-purple-600 text-white hover:bg-purple-700\"\n                }`}\n              >\n                {currentStep === steps.length ? \"Completed\" : \"Next\"}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CourseBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACS,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMa,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjD;IAAEF,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9C;IAAEF,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAK,CAAC,CAClD;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIZ,WAAW,GAAGQ,KAAK,CAACK,MAAM,EAAE;MAC9BZ,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMc,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAId,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,oBACEH,OAAA;IAAKkB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCnB,OAAA;MAAKkB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAIkB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEvB,OAAA;YAAGkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNvB,OAAA;UAAKkB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCnB,OAAA;YAAQkB,SAAS,EAAC,uFAAuF;YAAAC,QAAA,EAAC;UAE1G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvB,OAAA;YAAQkB,SAAS,EAAC,mFAAmF;YAAAC,QAAA,EAAC;UAEtG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvB,OAAA;YAAQkB,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EAAC;UAEjG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnBnB,OAAA;QAAKkB,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACtDnB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBR,KAAK,CAACa,GAAG,CAAEC,IAAI,iBACdzB,OAAA;YAEE0B,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAACqB,IAAI,CAACb,EAAE,CAAE;YACvCM,SAAS,EAAE,sEACTf,WAAW,KAAKsB,IAAI,CAACb,EAAE,GACnB,wDAAwD,GACxD,gCAAgC,EACnC;YAAAO,QAAA,gBAEHnB,OAAA;cAAMkB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEM,IAAI,CAACX;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAKkB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEM,IAAI,CAACZ;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CvB,OAAA;gBAAKkB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,OAAK,EAACM,IAAI,CAACb,EAAE;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,EACLpB,WAAW,KAAKsB,IAAI,CAACb,EAAE,iBACtBZ,OAAA;cAAKkB,SAAS,EAAC;YAA4C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAClE;UAAA,GAfIE,IAAI,CAACb,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,YAAY;QAAAC,QAAA,GAExBhB,WAAW,KAAK,CAAC,iBAChBH,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtFvB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnB,OAAA;cAAKkB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAOkB,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvB,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEvB,WAAY;kBACnBwB,QAAQ,EAAGC,CAAC,IAAKxB,cAAc,CAACwB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAChDI,WAAW,EAAC,yBAAyB;kBACrCd,SAAS,EAAC;gBAA0G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAOkB,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvB,OAAA;kBACE4B,KAAK,EAAErB,iBAAkB;kBACzBsB,QAAQ,EAAGC,CAAC,IAAKtB,oBAAoB,CAACsB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACtDI,WAAW,EAAC,kDAAkD;kBAC9DC,IAAI,EAAE,CAAE;kBACRf,SAAS,EAAC;gBAA0G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvB,OAAA;gBAAAmB,QAAA,gBACEnB,OAAA;kBAAOkB,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvB,OAAA;kBAAKkB,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBnB,OAAA;oBAAMkB,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9DvB,OAAA;oBACE2B,IAAI,EAAC,QAAQ;oBACbC,KAAK,EAAEnB,WAAY;oBACnBoB,QAAQ,EAAGC,CAAC,IAAKpB,cAAc,CAACwB,UAAU,CAACJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI,CAAC,CAAE;oBACjEI,WAAW,EAAC,MAAM;oBAClBd,SAAS,EAAC;kBAA+G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGApB,WAAW,KAAK,CAAC,iBAChBH,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EvB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,sCAAsC;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eACzGnB,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA4B;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACNvB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFvB,OAAA;gBAAGkB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAyD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/FvB,OAAA;gBAAQkB,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGApB,WAAW,KAAK,CAAC,iBAChBH,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFvB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,sCAAsC;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eACzGnB,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA+C;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,eACNvB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtFvB,OAAA;gBAAGkB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAA8D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpGvB,OAAA;gBAAQkB,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGApB,WAAW,KAAK,CAAC,iBAChBH,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFvB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,sCAAsC;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eACzGnB,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAA2I;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChN,CAAC,eACNvB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EvB,OAAA;gBAAGkB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,kBAAgB,EAACV,WAAW,CAACiC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9EvB,OAAA;gBAAQkB,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGApB,WAAW,KAAK,CAAC,iBAChBH,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAKkB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EvB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENvB,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnB,OAAA;cAAKkB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnB,OAAA;gBAAKkB,SAAS,EAAC,sCAAsC;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,gBACzGnB,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAkC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1GvB,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAyH;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9L,CAAC,eACNvB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5EvB,OAAA;gBAAGkB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,UAAQ,EAACd,WAAW,IAAI,iBAAiB;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFvB,OAAA;gBAAKkB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDnB,OAAA;kBAAQkB,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,EAAC;gBAEhH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvB,OAAA;kBAAQkB,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDvB,OAAA;UAAKkB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnEnB,OAAA;YACE0B,OAAO,EAAET,QAAS;YAClB0B,QAAQ,EAAExC,WAAW,KAAK,CAAE;YAC5Be,SAAS,EAAE,mCACTf,WAAW,KAAK,CAAC,GACb,8CAA8C,GAC9C,6CAA6C,EAChD;YAAAgB,QAAA,EACJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvB,OAAA;YAAKkB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnB,OAAA;cAAQkB,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAAC;YAE1G;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cACE0B,OAAO,EAAEX,QAAS;cAClB4B,QAAQ,EAAExC,WAAW,KAAKQ,KAAK,CAACK,MAAO;cACvCE,SAAS,EAAE,mCACTf,WAAW,KAAKQ,KAAK,CAACK,MAAM,GACxB,8CAA8C,GAC9C,8CAA8C,EACjD;cAAAG,QAAA,EAEFhB,WAAW,KAAKQ,KAAK,CAACK,MAAM,GAAG,WAAW,GAAG;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA1QID,aAAuB;AAAA2C,EAAA,GAAvB3C,aAAuB;AA4Q7B,eAAeA,aAAa;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}