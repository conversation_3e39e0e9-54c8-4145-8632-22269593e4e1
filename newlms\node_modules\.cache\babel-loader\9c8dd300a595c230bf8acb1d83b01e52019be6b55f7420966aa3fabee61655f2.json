{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\App.js\";\nimport React, { useState } from 'react';\nimport './index.css';\nimport CourseDetail from \"./CourseDetail.tsx\";\nimport CourseBuilder from \"./CourseBuilder.tsx\";\n\n// Icons\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this);\n_c = HomeIcon;\nconst ChatIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 14,\n  columnNumber: 3\n}, this);\n\n// Folder icon used in the navigation tabs\n_c2 = ChatIcon;\nconst FolderIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 21,\n  columnNumber: 3\n}, this);\n_c3 = FolderIcon;\nconst BookmarkIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 27,\n  columnNumber: 3\n}, this);\n_c4 = BookmarkIcon;\nconst UserIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 33,\n  columnNumber: 3\n}, this);\n_c5 = UserIcon;\nconst ClockIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 39,\n  columnNumber: 3\n}, this);\n_c6 = ClockIcon;\nconst DocumentIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n_c7 = DocumentIcon;\nconst SettingsIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-6 w-6\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 51,\n  columnNumber: 3\n}, this);\n_c8 = SettingsIcon;\nconst PlusIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 4v16m8-8H4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 58,\n  columnNumber: 3\n}, this);\n_c9 = PlusIcon;\nconst FilterIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 64,\n  columnNumber: 3\n}, this);\n_c0 = FilterIcon;\nconst GridIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 70,\n  columnNumber: 3\n}, this);\n_c1 = GridIcon;\nconst ListIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  fill: \"none\",\n  viewBox: \"0 0 24 24\",\n  stroke: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 76,\n  columnNumber: 3\n}, this);\n_c10 = ListIcon;\nconst PlayIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  viewBox: \"0 0 20 20\",\n  fill: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n    clipRule: \"evenodd\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 82,\n  columnNumber: 3\n}, this);\n_c11 = PlayIcon;\nconst ChevronDownIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  viewBox: \"0 0 20 20\",\n  fill: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n    clipRule: \"evenodd\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 88,\n  columnNumber: 3\n}, this);\n_c12 = ChevronDownIcon;\nconst MoreIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  className: \"h-5 w-5\",\n  viewBox: \"0 0 20 20\",\n  fill: \"currentColor\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 94,\n  columnNumber: 3\n}, this);\n\n// Course Card Component\n_c13 = MoreIcon;\nconst CourseCard = ({\n  title,\n  category,\n  enrolled,\n  accuracy,\n  completion,\n  urgency,\n  edited,\n  questions\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg overflow-hidden shadow-md mb-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-40 bg-gradient-to-r from-blue-400 to-indigo-500 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [enrolled, \" Enrolled\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), urgency === 'Urgent' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full\",\n          children: urgency\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(PlayIcon, {\n          className: \"text-white h-12 w-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-bold text-lg mb-2\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 text-sm\",\n            children: \"Accuracy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 w-6 rounded-full bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium\",\n              children: [accuracy, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 text-sm\",\n            children: \"Completion Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 w-6 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center text-xs font-medium\",\n              children: [completion, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: urgency === 'Not Urgent' ? 'Not Urgent' : urgency\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mt-4 pt-2 border-t border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: [\"Edited \", edited]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: [questions, \" Question\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(MoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_c14 = CourseCard;\nfunction App() {\n  const courses = [{\n    id: 1,\n    title: 'Mastering UI Design for Impactful Solutions',\n    category: 'UI/UX',\n    enrolled: 15,\n    accuracy: 40,\n    completion: 60,\n    urgency: 'Not Urgent',\n    edited: '2h ago',\n    questions: 10\n  }, {\n    id: 2,\n    title: 'A Symphony of Colors in UI Design',\n    category: 'Instructional Design',\n    enrolled: 21,\n    accuracy: 20,\n    completion: 80,\n    urgency: 'Not Urgent',\n    edited: '1h ago',\n    questions: 15\n  }, {\n    id: 3,\n    title: 'Bridging Users and UI in Design Harmony',\n    category: 'Experience Design',\n    enrolled: 18,\n    accuracy: 100,\n    completion: 100,\n    urgency: 'Urgent',\n    edited: '21h ago',\n    questions: 25\n  }, {\n    id: 4,\n    title: 'Creating Engaging Learning Journeys: UI/UX Best Practices',\n    category: 'UI/UX',\n    enrolled: 9,\n    accuracy: 20,\n    completion: 100,\n    urgency: 'Urgent',\n    edited: '5d ago',\n    questions: 30\n  }, {\n    id: 5,\n    title: 'Designing Intuitive User Interfaces',\n    category: 'User Interface (UI)',\n    enrolled: 32,\n    accuracy: 80,\n    completion: 60,\n    urgency: 'Not Urgent',\n    edited: '2d ago',\n    questions: 15\n  }, {\n    id: 6,\n    title: 'Optimizing User Experience in Educational Platforms',\n    category: 'User Experience',\n    enrolled: 7,\n    accuracy: null,\n    completion: null,\n    urgency: 'Urgent',\n    edited: '4d ago',\n    questions: 28\n  }];\n  return /*#__PURE__*/_jsxDEV(CourseDetail, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 10\n  }, this);\n}\n_c15 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"HomeIcon\");\n$RefreshReg$(_c2, \"ChatIcon\");\n$RefreshReg$(_c3, \"FolderIcon\");\n$RefreshReg$(_c4, \"BookmarkIcon\");\n$RefreshReg$(_c5, \"UserIcon\");\n$RefreshReg$(_c6, \"ClockIcon\");\n$RefreshReg$(_c7, \"DocumentIcon\");\n$RefreshReg$(_c8, \"SettingsIcon\");\n$RefreshReg$(_c9, \"PlusIcon\");\n$RefreshReg$(_c0, \"FilterIcon\");\n$RefreshReg$(_c1, \"GridIcon\");\n$RefreshReg$(_c10, \"ListIcon\");\n$RefreshReg$(_c11, \"PlayIcon\");\n$RefreshReg$(_c12, \"ChevronDownIcon\");\n$RefreshReg$(_c13, \"MoreIcon\");\n$RefreshReg$(_c14, \"CourseCard\");\n$RefreshReg$(_c15, \"App\");", "map": {"version": 3, "names": ["React", "useState", "CourseDetail", "CourseBuilder", "jsxDEV", "_jsxDEV", "HomeIcon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ChatIcon", "_c2", "FolderIcon", "_c3", "BookmarkIcon", "_c4", "UserIcon", "_c5", "ClockIcon", "_c6", "DocumentIcon", "_c7", "SettingsIcon", "_c8", "PlusIcon", "_c9", "FilterIcon", "_c0", "GridIcon", "_c1", "ListIcon", "_c10", "PlayIcon", "fillRule", "clipRule", "_c11", "ChevronDownIcon", "_c12", "MoreIcon", "_c13", "CourseCard", "title", "category", "enrolled", "accuracy", "completion", "urgency", "edited", "questions", "_c14", "App", "courses", "id", "_c15", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './index.css';\nimport CourseDetail from \"./CourseDetail.tsx\";\nimport CourseBuilder from \"./CourseBuilder.tsx\";\n\n// Icons\nconst HomeIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n  </svg>\n);\n\nconst ChatIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n  </svg>\n);\n\n// Folder icon used in the navigation tabs\nconst FolderIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\n  </svg>\n);\n\nconst BookmarkIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\n  </svg>\n);\n\nconst UserIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n  </svg>\n);\n\nconst ClockIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n  </svg>\n);\n\nconst DocumentIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n  </svg>\n);\n\nconst SettingsIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n  </svg>\n);\n\nconst PlusIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n  </svg>\n);\n\nconst FilterIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\" />\n  </svg>\n);\n\nconst GridIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n  </svg>\n);\n\nconst ListIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n  </svg>\n);\n\nconst PlayIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n  </svg>\n);\n\nconst ChevronDownIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n    <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n  </svg>\n);\n\nconst MoreIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n    <path d=\"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z\" />\n  </svg>\n);\n\n// Course Card Component\nconst CourseCard = ({ title, category, enrolled, accuracy, completion, urgency, edited, questions }) => {\n  return (\n    <div className=\"bg-white rounded-lg overflow-hidden shadow-md mb-6\">\n      <div className=\"relative\">\n        <div className=\"h-40 bg-gradient-to-r from-blue-400 to-indigo-500 flex items-center justify-center\">\n          <div className=\"absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full flex items-center\">\n            <span>{enrolled} Enrolled</span>\n          </div>\n          {urgency === 'Urgent' && (\n            <div className=\"absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full\">\n              {urgency}\n            </div>\n          )}\n          <PlayIcon className=\"text-white h-12 w-12\" />\n        </div>\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"font-bold text-lg mb-2\">{title}</h3>\n        <div className=\"flex justify-between items-center mb-4\">\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-gray-600 text-sm\">Accuracy</span>\n            <div className=\"flex items-center\">\n              <div className=\"h-6 w-6 rounded-full bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium\">\n                {accuracy}%\n              </div>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-gray-600 text-sm\">Completion Rate</span>\n            <div className=\"flex items-center\">\n              <div className=\"h-6 w-6 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center text-xs font-medium\">\n                {completion}%\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-500\">{category}</div>\n          <div className=\"text-sm text-gray-500\">{urgency === 'Not Urgent' ? 'Not Urgent' : urgency}</div>\n        </div>\n        <div className=\"flex justify-between items-center mt-4 pt-2 border-t border-gray-100\">\n          <div className=\"text-xs text-gray-500\">Edited {edited}</div>\n          <div className=\"flex items-center space-x-1\">\n            <span className=\"text-xs\">{questions} Question</span>\n          </div>\n          <button className=\"text-gray-500 hover:text-gray-700\">\n            <MoreIcon />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction App() {\n  const courses = [\n    {\n      id: 1,\n      title: 'Mastering UI Design for Impactful Solutions',\n      category: 'UI/UX',\n      enrolled: 15,\n      accuracy: 40,\n      completion: 60,\n      urgency: 'Not Urgent',\n      edited: '2h ago',\n      questions: 10\n    },\n    {\n      id: 2,\n      title: 'A Symphony of Colors in UI Design',\n      category: 'Instructional Design',\n      enrolled: 21,\n      accuracy: 20,\n      completion: 80,\n      urgency: 'Not Urgent',\n      edited: '1h ago',\n      questions: 15\n    },\n    {\n      id: 3,\n      title: 'Bridging Users and UI in Design Harmony',\n      category: 'Experience Design',\n      enrolled: 18,\n      accuracy: 100,\n      completion: 100,\n      urgency: 'Urgent',\n      edited: '21h ago',\n      questions: 25\n    },\n    {\n      id: 4,\n      title: 'Creating Engaging Learning Journeys: UI/UX Best Practices',\n      category: 'UI/UX',\n      enrolled: 9,\n      accuracy: 20,\n      completion: 100,\n      urgency: 'Urgent',\n      edited: '5d ago',\n      questions: 30\n    },\n    {\n      id: 5,\n      title: 'Designing Intuitive User Interfaces',\n      category: 'User Interface (UI)',\n      enrolled: 32,\n      accuracy: 80,\n      completion: 60,\n      urgency: 'Not Urgent',\n      edited: '2d ago',\n      questions: 15\n    },\n    {\n      id: 6,\n      title: 'Optimizing User Experience in Educational Platforms',\n      category: 'User Experience',\n      enrolled: 7,\n      accuracy: null,\n      completion: null,\n      urgency: 'Urgent',\n      edited: '4d ago',\n      questions: 28\n    }\n  ];\n\n  return <CourseDetail />;\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAA,kBACfD,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAkJ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvN,CACN;AAACC,EAAA,GAJIf,QAAQ;AAMd,MAAMgB,QAAQ,GAAGA,CAAA,kBACfjB,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAA+J;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACpO,CACN;;AAED;AAAAG,GAAA,GANMD,QAAQ;AAOd,MAAME,UAAU,GAAGA,CAAA,kBACjBnB,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAA2E;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAChJ,CACN;AAACK,GAAA,GAJID,UAAU;AAMhB,MAAME,YAAY,GAAGA,CAAA,kBACnBrB,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAmD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxH,CACN;AAACO,GAAA,GAJID,YAAY;AAMlB,MAAME,QAAQ,GAAGA,CAAA,kBACfvB,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAqE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1I,CACN;AAACS,GAAA,GAJID,QAAQ;AAMd,MAAME,SAAS,GAAGA,CAAA,kBAChBzB,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAA6C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClH,CACN;AAACW,GAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAGA,CAAA,kBACnB3B,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAsH;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3L,CACN;AAACa,GAAA,GAJID,YAAY;AAMlB,MAAME,YAAY,GAAGA,CAAA,kBACnB7B,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,gBAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAqe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7iBf,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAkC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvG,CACN;AAACe,GAAA,GALID,YAAY;AAOlB,MAAME,QAAQ,GAAGA,CAAA,kBACf/B,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACrF,CACN;AAACiB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAGA,CAAA,kBACjBjC,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAyJ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC9N,CACN;AAACmB,GAAA,GAJID,UAAU;AAMhB,MAAME,QAAQ,GAAGA,CAAA,kBACfnC,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAsQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3U,CACN;AAACqB,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGA,CAAA,kBACfrC,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACC,IAAI,EAAC,MAAM;EAACC,OAAO,EAAC,WAAW;EAACC,MAAM,EAAC,cAAc;EAAAC,QAAA,eAC/GP,OAAA;IAAMQ,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC,OAAO;IAACC,WAAW,EAAE,CAAE;IAACC,CAAC,EAAC;EAAiC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACtG,CACN;AAACuB,IAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGA,CAAA,kBACfvC,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACE,OAAO,EAAC,WAAW;EAACD,IAAI,EAAC,cAAc;EAAAG,QAAA,eACjGP,OAAA;IAAMwC,QAAQ,EAAC,SAAS;IAAC7B,CAAC,EAAC,yGAAyG;IAAC8B,QAAQ,EAAC;EAAS;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvJ,CACN;AAAC2B,IAAA,GAJIH,QAAQ;AAMd,MAAMI,eAAe,GAAGA,CAAA,kBACtB3C,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACE,OAAO,EAAC,WAAW;EAACD,IAAI,EAAC,cAAc;EAAAG,QAAA,eACjGP,OAAA;IAAMwC,QAAQ,EAAC,SAAS;IAAC7B,CAAC,EAAC,oHAAoH;IAAC8B,QAAQ,EAAC;EAAS;IAAA7B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClK,CACN;AAAC6B,IAAA,GAJID,eAAe;AAMrB,MAAME,QAAQ,GAAGA,CAAA,kBACf7C,OAAA;EAAKE,KAAK,EAAC,4BAA4B;EAACC,SAAS,EAAC,SAAS;EAACE,OAAO,EAAC,WAAW;EAACD,IAAI,EAAC,cAAc;EAAAG,QAAA,eACjGP,OAAA;IAAMW,CAAC,EAAC;EAAgG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxG,CACN;;AAED;AAAA+B,IAAA,GANMD,QAAQ;AAOd,MAAME,UAAU,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,UAAU;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAU,CAAC,KAAK;EACtG,oBACEvD,OAAA;IAAKG,SAAS,EAAC,oDAAoD;IAAAI,QAAA,gBACjEP,OAAA;MAAKG,SAAS,EAAC,UAAU;MAAAI,QAAA,eACvBP,OAAA;QAAKG,SAAS,EAAC,oFAAoF;QAAAI,QAAA,gBACjGP,OAAA;UAAKG,SAAS,EAAC,+FAA+F;UAAAI,QAAA,eAC5GP,OAAA;YAAAO,QAAA,GAAO2C,QAAQ,EAAC,WAAS;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACLsC,OAAO,KAAK,QAAQ,iBACnBrD,OAAA;UAAKG,SAAS,EAAC,6EAA6E;UAAAI,QAAA,EACzF8C;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eACDf,OAAA,CAACuC,QAAQ;UAACpC,SAAS,EAAC;QAAsB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNf,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAI,QAAA,gBAClBP,OAAA;QAAIG,SAAS,EAAC,wBAAwB;QAAAI,QAAA,EAAEyC;MAAK;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnDf,OAAA;QAAKG,SAAS,EAAC,wCAAwC;QAAAI,QAAA,gBACrDP,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1CP,OAAA;YAAMG,SAAS,EAAC,uBAAuB;YAAAI,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDf,OAAA;YAAKG,SAAS,EAAC,mBAAmB;YAAAI,QAAA,eAChCP,OAAA;cAAKG,SAAS,EAAC,uGAAuG;cAAAI,QAAA,GACnH4C,QAAQ,EAAC,GACZ;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1CP,OAAA;YAAMG,SAAS,EAAC,uBAAuB;YAAAI,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9Df,OAAA;YAAKG,SAAS,EAAC,mBAAmB;YAAAI,QAAA,eAChCP,OAAA;cAAKG,SAAS,EAAC,qGAAqG;cAAAI,QAAA,GACjH6C,UAAU,EAAC,GACd;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNf,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDP,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAAI,QAAA,EAAE0C;QAAQ;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDf,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAAI,QAAA,EAAE8C,OAAO,KAAK,YAAY,GAAG,YAAY,GAAGA;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACNf,OAAA;QAAKG,SAAS,EAAC,sEAAsE;QAAAI,QAAA,gBACnFP,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAAI,QAAA,GAAC,SAAO,EAAC+C,MAAM;QAAA;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5Df,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAI,QAAA,eAC1CP,OAAA;YAAMG,SAAS,EAAC,SAAS;YAAAI,QAAA,GAAEgD,SAAS,EAAC,WAAS;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNf,OAAA;UAAQG,SAAS,EAAC,mCAAmC;UAAAI,QAAA,eACnDP,OAAA,CAAC6C,QAAQ;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyC,IAAA,GApDIT,UAAU;AAsDhB,SAASU,GAAGA,CAAA,EAAG;EACb,MAAMC,OAAO,GAAG,CACd;IACEC,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,6CAA6C;IACpDC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,mCAAmC;IAC1CC,QAAQ,EAAE,sBAAsB;IAChCC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,yCAAyC;IAChDC,QAAQ,EAAE,mBAAmB;IAC7BC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE;EACb,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,2DAA2D;IAClEC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,qBAAqB;IAC/BC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEI,EAAE,EAAE,CAAC;IACLX,KAAK,EAAE,qDAAqD;IAC5DC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBAAOvD,OAAA,CAACH,YAAY;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzB;AAAC6C,IAAA,GAvEQH,GAAG;AAyEZ,eAAeA,GAAG;AAAC,IAAAzC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAU,IAAA,EAAAI,IAAA;AAAAC,YAAA,CAAA7C,EAAA;AAAA6C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,IAAA;AAAAuB,YAAA,CAAAnB,IAAA;AAAAmB,YAAA,CAAAjB,IAAA;AAAAiB,YAAA,CAAAf,IAAA;AAAAe,YAAA,CAAAL,IAAA;AAAAK,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}