[{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx": "5"}, {"size": 535, "mtime": 1747410977891, "results": "6", "hashOfConfig": "7"}, {"size": 362, "mtime": 1747410980121, "results": "8", "hashOfConfig": "7"}, {"size": 10856, "mtime": 1748534845823, "results": "9", "hashOfConfig": "7"}, {"size": 32923, "mtime": 1748532930294, "results": "10", "hashOfConfig": "7"}, {"size": 12490, "mtime": 1748534992479, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12e2umx", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js", ["27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx", [], [], {"ruleId": "42", "severity": 1, "message": "43", "line": 7, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 7, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "46", "line": 13, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 13, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "47", "line": 20, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 20, "endColumn": 17}, {"ruleId": "42", "severity": 1, "message": "48", "line": 26, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 26, "endColumn": 19}, {"ruleId": "42", "severity": 1, "message": "49", "line": 32, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 32, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "50", "line": 38, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 38, "endColumn": 16}, {"ruleId": "42", "severity": 1, "message": "51", "line": 44, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 44, "endColumn": 19}, {"ruleId": "42", "severity": 1, "message": "52", "line": 50, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 50, "endColumn": 19}, {"ruleId": "42", "severity": 1, "message": "53", "line": 57, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 57, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "54", "line": 63, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 63, "endColumn": 17}, {"ruleId": "42", "severity": 1, "message": "55", "line": 69, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 69, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "56", "line": 75, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 75, "endColumn": 15}, {"ruleId": "42", "severity": 1, "message": "57", "line": 87, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 87, "endColumn": 22}, {"ruleId": "42", "severity": 1, "message": "58", "line": 100, "column": 7, "nodeType": "44", "messageId": "45", "endLine": 100, "endColumn": 17}, {"ruleId": "42", "severity": 1, "message": "59", "line": 157, "column": 9, "nodeType": "44", "messageId": "45", "endLine": 157, "endColumn": 16}, "no-unused-vars", "'HomeIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'ChatIcon' is assigned a value but never used.", "'FolderIcon' is assigned a value but never used.", "'BookmarkIcon' is assigned a value but never used.", "'UserIcon' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'DocumentIcon' is assigned a value but never used.", "'SettingsIcon' is assigned a value but never used.", "'PlusIcon' is assigned a value but never used.", "'FilterIcon' is assigned a value but never used.", "'GridIcon' is assigned a value but never used.", "'ListIcon' is assigned a value but never used.", "'ChevronDownIcon' is assigned a value but never used.", "'CourseCard' is assigned a value but never used.", "'courses' is assigned a value but never used."]