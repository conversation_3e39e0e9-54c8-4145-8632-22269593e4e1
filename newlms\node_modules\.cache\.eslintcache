[{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx": "4"}, {"size": 535, "mtime": 1747410977891, "results": "5", "hashOfConfig": "6"}, {"size": 362, "mtime": 1747410980121, "results": "7", "hashOfConfig": "6"}, {"size": 9649, "mtime": 1748531828724, "results": "8", "hashOfConfig": "6"}, {"size": 23995, "mtime": 1748532486183, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12e2umx", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js", ["22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx", [], [], {"ruleId": "37", "severity": 1, "message": "38", "line": 6, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 6, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "41", "line": 12, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 12, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "42", "line": 19, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 19, "endColumn": 17}, {"ruleId": "37", "severity": 1, "message": "43", "line": 25, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 25, "endColumn": 19}, {"ruleId": "37", "severity": 1, "message": "44", "line": 31, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 31, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "45", "line": 37, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 37, "endColumn": 16}, {"ruleId": "37", "severity": 1, "message": "46", "line": 43, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 43, "endColumn": 19}, {"ruleId": "37", "severity": 1, "message": "47", "line": 49, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 49, "endColumn": 19}, {"ruleId": "37", "severity": 1, "message": "48", "line": 56, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 56, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "49", "line": 62, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 62, "endColumn": 17}, {"ruleId": "37", "severity": 1, "message": "50", "line": 68, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 68, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "51", "line": 74, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 74, "endColumn": 15}, {"ruleId": "37", "severity": 1, "message": "52", "line": 86, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 86, "endColumn": 22}, {"ruleId": "37", "severity": 1, "message": "53", "line": 99, "column": 7, "nodeType": "39", "messageId": "40", "endLine": 99, "endColumn": 17}, {"ruleId": "37", "severity": 1, "message": "54", "line": 154, "column": 9, "nodeType": "39", "messageId": "40", "endLine": 154, "endColumn": 16}, "no-unused-vars", "'HomeIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'ChatIcon' is assigned a value but never used.", "'FolderIcon' is assigned a value but never used.", "'BookmarkIcon' is assigned a value but never used.", "'UserIcon' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'DocumentIcon' is assigned a value but never used.", "'SettingsIcon' is assigned a value but never used.", "'PlusIcon' is assigned a value but never used.", "'FilterIcon' is assigned a value but never used.", "'GridIcon' is assigned a value but never used.", "'ListIcon' is assigned a value but never used.", "'ChevronDownIcon' is assigned a value but never used.", "'CourseCard' is assigned a value but never used.", "'courses' is assigned a value but never used."]